# 任务创建工作流接口文档

## 概述

本文档描述了SAST任务创建的完整工作流程，包含两个主要步骤：
1. **第一步**: 填写任务信息（任务名称、编程语言、分析方式等）
2. **第二步**: 上传文件信息（源代码文件、预编译文件等）

## 工作流程

### 步骤1: 创建任务基本信息

**接口**: `POST /api/project-management/{projectId}/tasks`

**请求参数**:
```json
{
  "taskName": "struts检测",
  "taskLanguage": "Java", 
  "analysisMethod": "离线分析",
  "buildMethod": "Java-maven",
  "taskDescription": "对struts框架进行安全检测分析"
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "taskId": 123456,
    "taskName": "struts检测",
    "taskStatus": "排队"
  }
}
```

### 步骤2: 上传任务文件

**接口**: `POST /api/tasks/{taskId}/files/upload`

**请求参数**: 
- `fileType`: "源码文件" 或 "预编译压缩包"
- `file`: 上传的文件（multipart/form-data）

**响应示例**:
```json
{
  "code": 0,
  "msg": "string", 
  "data": {
    "fileId": 789,
    "fileName": "xxx模型源码编译文件.xlsx",
    "fileSize": 1048576,
    "uploadTime": "2025-01-29T10:30:00"
  }
}
```

## 支持接口

### 1. 获取编程语言列表
**接口**: `GET /api/system/programming-languages`

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {"value": "Java", "label": "Java"},
    {"value": "Python", "label": "Python"},
    {"value": "Go", "label": "Go"},
    {"value": "C++", "label": "C++"}
  ]
}
```

### 2. 获取分析方式列表
**接口**: `GET /api/system/analysis-methods`

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {"value": "离线分析", "label": "离线分析"},
    {"value": "在线分析", "label": "在线分析"}
  ]
}
```

### 3. 获取构建方法列表
**接口**: `GET /api/system/build-methods`

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {"value": "Java-maven", "label": "Java-maven"},
    {"value": "Java-gradle", "label": "Java-gradle"},
    {"value": "Python-pip", "label": "Python-pip"}
  ]
}
```

## 文件管理接口

### 1. 获取任务文件列表
**接口**: `GET /api/tasks/{taskId}/files`

### 2. 删除任务文件
**接口**: `DELETE /api/tasks/{taskId}/files/{fileId}`

### 3. 启动任务分析
**接口**: `POST /api/tasks/{taskId}/start`

### 4. 获取任务状态
**接口**: `GET /api/tasks/{taskId}/status`

## 数据模型

### 任务信息字段映射

| 原型图字段 | 数据库字段 | 类型 | 必填 | 说明 |
|-----------|-----------|------|------|------|
| 任务名称 | task_name | varchar(255) | 是 | 任务的显示名称 |
| 编程语言 | task_language | varchar(100) | 是 | Java/Python/Go等 |
| 分析方式 | - | - | 是 | 离线分析/在线分析 |
| 构建方法 | - | - | 否 | 在线分析时必填 |

### 文件信息字段映射

| 原型图字段 | 数据库字段 | 类型 | 说明 |
|-----------|-----------|------|------|
| 文件类型 | file_type | varchar(50) | 源码文件/预编译压缩包 |
| 文件名 | file_name | varchar(255) | 上传的文件名 |
| 文件路径 | file_path | varchar(500) | 服务器存储路径 |
| 文件大小 | file_size | bigint | 文件大小（字节） |
| 上传时间 | upload_time | timestamp | 文件上传时间 |

## 业务规则

### 1. 任务创建规则
- 任务名称不能重复（同一项目内）
- 编程语言必须从预定义列表中选择
- 选择"在线分析"时，构建方法为必填项
- 选择"离线分析"时，构建方法可选

### 2. 文件上传规则
- 支持的文件格式：tar.gz、zip、jar等压缩包
- 单个文件大小限制：建议不超过100MB
- 每个任务可以上传多个文件
- 文件类型必须明确指定（源码文件或预编译压缩包）

### 3. 任务状态流转
```
排队 → 传播器查找 → 链路分析 → 研判 → POC验证 → 修复建议生成 → AI分析完成
                                                                    ↓
                                                                分析失败
```

## 错误处理

所有接口遵循统一的错误响应格式：
```json
{
  "code": 400,
  "msg": "参数错误",
  "data": null
}
```

常见错误码：
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 权限要求

- 所有接口需要用户登录认证
- 任务创建需要项目管理权限
- 文件上传需要对应任务的操作权限
- 系统配置接口需要基础访问权限

## 注意事项

1. 所有时间字段均为ISO 8601格式
2. 文件上传使用multipart/form-data格式
3. 任务创建后会自动生成唯一的任务ID
4. 文件上传支持断点续传（可选实现）
5. 大文件上传建议使用分片上传
6. 删除任务时会同时删除关联的所有文件
7. 任务状态变更会触发相应的通知机制
