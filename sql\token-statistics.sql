-- auto-generated definition
create table app_sast_token_statistics
(
    id                 bigint         default snow_next_id_bigint()    not null
        primary key,
    task_id            bigint                                          not null
        constraint fk_token_stats_task
            references app_sast_task_management,
    stage_id           bigint                                          not null
        constraint fk_token_stats_stage
            references app_sast_task_stages,
    model_id           bigint                                          not null
        constraint fk_token_stats_model
            references app_sast_model_config,
    vulnerability_type varchar(100),
    stage_type         varchar(100)                                    not null,
    input_tokens       integer        default 0                        not null,
    output_tokens      integer        default 0                        not null,
    total_tokens       integer        default 0                        not null,
    cost_amount        numeric(10, 6) default 0                        not null,
    currency           varchar(10)    default 'USD'::character varying not null,
    created_id         bigint                                          not null,
    created_name       varchar(50)                                     not null,
    created_time       timestamp      default now(),
    modify_id          bigint,
    modify_name        varchar(50),
    modify_time        timestamp      default now(),
    del_flag           smallint       default 0                        not null
);

comment on table app_sast_token_statistics is 'SAST Token使用统计表';

comment on column app_sast_token_statistics.id is '主键ID';

comment on column app_sast_token_statistics.task_id is '关联任务ID，外键关联';

comment on column app_sast_token_statistics.stage_id is '关联阶段ID，外键关联';

comment on column app_sast_token_statistics.model_id is '关联模型ID，外键关联';

comment on column app_sast_token_statistics.vulnerability_type is '漏洞类型，可空';

comment on column app_sast_token_statistics.stage_type is '阶段类型：外部函数分类/净化函数提取/漏洞研判等';

comment on column app_sast_token_statistics.input_tokens is '输入token数，默认0';

comment on column app_sast_token_statistics.output_tokens is '输出token数，默认0';

comment on column app_sast_token_statistics.total_tokens is '总token数，默认0';

comment on column app_sast_token_statistics.cost_amount is '费用金额，默认0';

comment on column app_sast_token_statistics.currency is '货币类型，默认USD';

comment on column app_sast_token_statistics.created_id is '创建人ID';

comment on column app_sast_token_statistics.created_name is '创建人姓名';

comment on column app_sast_token_statistics.created_time is '创建时间';

comment on column app_sast_token_statistics.modify_id is '更新人ID';

comment on column app_sast_token_statistics.modify_name is '更新人姓名';

comment on column app_sast_token_statistics.modify_time is '更新时间';

comment on column app_sast_token_statistics.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_token_statistics
    owner to postgres;

create index idx_app_sast_token_statistics_task_id
    on app_sast_token_statistics (task_id);

create index idx_app_sast_token_statistics_stage_id
    on app_sast_token_statistics (stage_id);

create index idx_app_sast_token_statistics_model_id
    on app_sast_token_statistics (model_id);

create index idx_app_sast_token_statistics_stage_type
    on app_sast_token_statistics (stage_type);

