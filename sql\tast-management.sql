-- auto-generated definition
create table app_sast_task_management
(
    task_id          bigint      default snow_next_id_bigint()     not null
        primary key,
    project_id       bigint                                        not null
        constraint fk_task_project
            references app_sast_project_management,
    task_name        varchar(255)                                  not null,
    task_language    varchar(100)                                  not null,
    task_model_used  varchar(255)                                  not null,
    task_description text,
    task_status      varchar(50) default '排队'::character varying not null,
    created_id       bigint                                        not null,
    created_name     varchar(50)                                   not null,
    created_time     timestamp   default now(),
    modify_id        bigint,
    modify_name      varchar(50),
    modify_time      timestamp   default now(),
    del_flag         smallint    default '0'::smallint             not null
);

comment on table app_sast_task_management is 'SAST任务管理表';

comment on column app_sast_task_management.task_id is '主键ID，自增';

comment on column app_sast_task_management.project_id is '所属项目ID，外键关联';

comment on column app_sast_task_management.task_name is '任务名称，必填';

comment on column app_sast_task_management.task_language is '编程语言，如 Java/Python/Go';

comment on column app_sast_task_management.task_model_used is '使用的大模型名称，如 GPT-4，可多个，逗号隔开';

comment on column app_sast_task_management.task_description is '任务描述，可空';

comment on column app_sast_task_management.task_status is '任务状态，如 排队/传播器查找/链路分析/研判/POC验证/修复建议生成';

comment on column app_sast_task_management.created_name is '创建人';

comment on column app_sast_task_management.created_time is '创建时间';

comment on column app_sast_task_management.modify_id is '更新人标识';

comment on column app_sast_task_management.modify_name is '更新人';

comment on column app_sast_task_management.modify_time is '更新时间';

comment on column app_sast_task_management.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_task_management
    owner to postgres;

create index idx_app_sast_task_management_project_id
    on app_sast_task_management (project_id);

create index idx_app_sast_task_management_status
    on app_sast_task_management (task_status);

create index idx_app_sast_task_management_language
    on app_sast_task_management (task_language);

