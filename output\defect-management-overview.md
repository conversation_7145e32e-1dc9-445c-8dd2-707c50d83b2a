# 缺陷管理系统接口文档

## 概述

缺陷管理系统提供完整的SAST安全缺陷管理功能，包括缺陷统计、列表查询、详情查看、验证操作、修复管理等核心功能。

## 页面功能分析

### 统计卡片区域
- **漏洞总数**: 265个
- **高危漏洞**: 140个 (占比30%)
- **中危漏洞**: 109个 (占比30%)  
- **低危漏洞**: 87个 (占比40%)

### 列表功能
- **搜索**: 支持按缺陷名称/漏洞类型搜索
- **分页**: 每页10条记录，支持翻页
- **排序**: 按创建时间等字段排序
- **筛选**: 支持按项目、任务、危险等级、验证结果筛选

### 操作功能
- **查看详情**: 点击记录查看缺陷详细信息
- **验证缺陷**: 对缺陷进行验证操作
- **修复缺陷**: 标记缺陷为已修复状态
- **批量操作**: 支持批量验证、批量修复
- **导出报告**: 导出缺陷信息为Excel/PDF格式

## 核心接口

### 1. 缺陷统计接口
**GET** `/api/defects/statistics`

获取缺陷总数和各危险等级的统计数据，支持按项目和任务筛选。

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "totalCount": 265,
    "highRiskCount": 140,
    "highRiskPercentage": 30,
    "mediumRiskCount": 109,
    "mediumRiskPercentage": 30,
    "lowRiskCount": 87,
    "lowRiskPercentage": 40
  }
}
```

### 2. 缺陷列表接口
**GET** `/api/defects`

分页查询缺陷信息列表，支持搜索和多维度筛选。

**查询参数**:
- `current`: 当前页码 (默认1)
- `size`: 每页记录数 (默认10)
- `keyword`: 搜索关键词
- `projectId`: 项目ID筛选
- `taskId`: 任务ID筛选
- `defectLevel`: 危险程度筛选
- `verificationResult`: 验证结果筛选

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "records": [
      {
        "id": 1,
        "sequenceNumber": "01",
        "vulnerabilityType": "SQL注入",
        "projectName": "测试智能检测试",
        "defectPath": "VulnCore/JDBCAttack/src/main/java/hmy/BMIAttack.java",
        "defectLine": 22,
        "defectLevel": "高危",
        "verificationResult": "验证成功",
        "createdTime": "2025-03-12 12:23:11",
        "taskId": 123456,
        "projectId": 1
      }
    ],
    "total": 265,
    "size": 10,
    "current": 1,
    "pages": 27
  }
}
```

### 3. 缺陷详情接口
**GET** `/api/defects/{defectId}`

获取指定缺陷的详细信息，包括修复建议、链路信息、大模型检测结果等。

### 4. 缺陷验证接口
**POST** `/api/defects/{defectId}/verify`

对指定缺陷进行验证，更新验证结果。

**请求参数**:
```json
{
  "verificationResult": "验证成功",
  "verificationNote": "通过POC验证确认存在SQL注入漏洞"
}
```

### 5. 缺陷修复接口
**POST** `/api/defects/{defectId}/fix`

标记缺陷为已修复状态。

**请求参数**:
```json
{
  "fixNote": "已按照建议修复SQL注入漏洞"
}
```

## 批量操作接口

### 1. 批量验证
**POST** `/api/defects/batch-verify`

批量对多个缺陷进行验证操作。

### 2. 批量修复
**POST** `/api/defects/batch-fix`

批量标记多个缺陷为已修复状态。

### 3. 导出报告
**POST** `/api/defects/export`

导出缺陷信息为Excel或PDF格式。

## 数据模型

### 缺陷信息字段映射

| 原型图字段 | 数据库字段 | 类型 | 说明 |
|-----------|-----------|------|------|
| 序号 | - | - | 前端生成的序列号 |
| 漏洞类型 | vulnerability_type | varchar(100) | SQL注入、RCE等 |
| 所属项目 | project_name | varchar(255) | 关联项目表获取 |
| 缺陷对象 | defect_path | varchar(255) | 文件路径 |
| 缺陷行号 | defect_line | integer | 代码行号 |
| 危险程度 | defect_level | varchar(50) | 高危/中危/低危 |
| 验证结果 | defect_verification_result | varchar(50) | 验证成功/验证失败/未验证 |
| 创建时间 | created_time | timestamp | 缺陷发现时间 |

### 关联关系
- `task_id` → `app_sast_task_management.task_id`
- `project_id` → `app_sast_project_management.id`

## 业务规则

### 1. 统计规则
- 统计数据实时计算，基于当前有效缺陷（del_flag=0）
- 占比计算精确到整数百分比
- 支持按项目和任务维度统计

### 2. 列表查询规则
- 默认按创建时间倒序排列
- 搜索支持模糊匹配漏洞类型和缺陷路径
- 分页最大每页100条记录
- 软删除记录不显示在列表中

### 3. 验证规则
- 只有未验证和验证失败的缺陷可以重新验证
- 验证成功的缺陷不能修改验证结果
- 验证操作会记录操作人和操作时间

### 4. 修复规则
- 任何状态的缺陷都可以标记为已修复
- 修复操作不会删除缺陷记录，只更新状态
- 修复后的缺陷仍可查看详情和历史记录

## 权限控制

### 查看权限
- 项目成员可查看所属项目的缺陷信息
- 管理员可查看所有项目的缺陷信息

### 操作权限
- 项目管理员可对所属项目缺陷进行验证和修复操作
- 系统管理员可对所有缺陷进行操作
- 普通用户只能查看，不能操作

### 导出权限
- 需要导出权限才能使用导出功能
- 导出范围受查看权限限制

## 性能优化

### 1. 数据库优化
- 在 `task_id`、`project_id`、`vulnerability_type`、`defect_level` 字段上建立索引
- 统计查询使用缓存机制，定期更新
- 大数据量分页使用游标分页

### 2. 接口优化
- 列表查询支持字段选择，减少数据传输
- 统计数据使用Redis缓存，提高响应速度
- 导出功能使用异步处理，避免超时

### 3. 前端优化
- 列表使用虚拟滚动处理大数据量
- 搜索使用防抖机制，减少请求频率
- 批量操作使用进度条显示处理状态

## 错误处理

### 常见错误码
- `400`: 请求参数错误（如分页参数超出范围）
- `403`: 权限不足（如无权限查看指定项目缺陷）
- `404`: 资源不存在（如缺陷ID不存在）
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "code": 400,
  "msg": "请求参数错误",
  "data": null
}
```

## 扩展功能

### 1. 缺陷趋势分析
- 按时间维度统计缺陷发现趋势
- 按项目维度对比缺陷分布
- 按漏洞类型统计热点问题

### 2. 缺陷生命周期管理
- 记录缺陷从发现到修复的完整流程
- 支持缺陷状态流转和审批机制
- 提供缺陷处理时效统计

### 3. 智能推荐
- 基于历史数据推荐修复方案
- 智能识别相似缺陷模式
- 自动分类和优先级排序
