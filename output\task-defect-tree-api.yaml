openapi: 3.0.3
info:
  title: 任务缺陷树形结构API
  description: 根据任务ID获取缺陷分类树形结构接口
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.example.com
    description: 生产环境

paths:
  /tasks/{taskId}/defects/tree:
    get:
      tags:
        - 任务缺陷管理
      summary: 获取任务缺陷树形结构
      description: 根据任务ID获取缺陷的分类树形结构，按漏洞类型分组显示，支持搜索筛选
      operationId: getTaskDefectTree
      parameters:
        - name: taskId
          in: path
          description: 任务ID
          required: true
          schema:
            type: integer
            format: int64
            example: 123456
        - name: keyword
          in: query
          description: 搜索关键词（缺陷路径名称）
          required: false
          schema:
            type: string
            example: "VulnCore"
        - name: defectLevel
          in: query
          description: 危险程度筛选
          required: false
          schema:
            type: string
            enum: ["高危", "中危", "低危"]
            example: "高危"
      responses:
        '200':
          description: 成功获取缺陷树形结构
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    $ref: '#/components/schemas/DefectTree'
              example:
                code: 0
                msg: "string"
                data:
                  totalCount: 17
                  vulnerabilityTypes:
                    - typeName: "SQL注入"
                      typeCount: 5
                      defects:
                        - defectId: 1
                          sequenceNumber: 1
                          defectPath: "VulnCore/JDBCAttack/src/main/java/hmy/BMIAttack.java"
                          defectLine: 22
                          defectLevel: "高危"
                          verificationResult: "验证成功"
                        - defectId: 3
                          sequenceNumber: 3
                          defectPath: "ulnCore/Inject/SQL/src/main/java/com/spp/mysql/MysqlInject.java"
                          defectLine: 12
                          defectLevel: "高危"
                          verificationResult: "验证失败"
                        - defectId: 6
                          sequenceNumber: 6
                          defectPath: "VulnCore/JDBCAttack/src/main/java/hzd/database/HZAttack.java"
                          defectLine: 13
                          defectLevel: "中危"
                          verificationResult: "验证成功"
                        - defectId: 8
                          sequenceNumber: 8
                          defectPath: "VulnCore/Inject/SQL/src/main/java/com/spp/mysql/SpringJDBCInject.java"
                          defectLine: 11
                          defectLevel: "低危"
                          verificationResult: "验证失败"
                        - defectId: 10
                          sequenceNumber: 10
                          defectPath: "VulnCore/Inject/SQL/src/main/java/com/spp/mysql/MysqlInject.java"
                          defectLine: 93
                          defectLevel: "低危"
                          verificationResult: "验证成功"
                    - typeName: "RCE"
                      typeCount: 12
                      defects:
                        - defectId: 2
                          sequenceNumber: 2
                          defectPath: "VulnCore/XXE/src/main/java/org/example/XXEAttack.java"
                          defectLine: 56
                          defectLevel: "高危"
                          verificationResult: "验证成功"
                        - defectId: 4
                          sequenceNumber: 4
                          defectPath: "VulnCore/XXE/src/main/java/org/example/XXEAttack.java"
                          defectLine: 22
                          defectLevel: "高危"
                          verificationResult: "验证失败"
        '404':
          description: 任务不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /tasks/{taskId}/defects/tree/statistics:
    get:
      tags:
        - 任务缺陷管理
      summary: 获取任务缺陷分类统计
      description: 获取指定任务的缺陷按漏洞类型分类的统计信息
      operationId: getTaskDefectStatistics
      parameters:
        - name: taskId
          in: path
          description: 任务ID
          required: true
          schema:
            type: integer
            format: int64
            example: 123456
      responses:
        '200':
          description: 成功获取缺陷分类统计
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      totalCount:
                        type: integer
                        description: 缺陷总数
                        example: 17
                      typeStatistics:
                        type: array
                        description: 漏洞类型统计
                        items:
                          type: object
                          properties:
                            typeName:
                              type: string
                              description: 漏洞类型名称
                              example: "SQL注入"
                            count:
                              type: integer
                              description: 该类型缺陷数量
                              example: 5
                            percentage:
                              type: number
                              format: float
                              description: 占比（百分比）
                              example: 29.4
              example:
                code: 0
                msg: "string"
                data:
                  totalCount: 17
                  typeStatistics:
                    - typeName: "SQL注入"
                      count: 5
                      percentage: 29.4
                    - typeName: "RCE"
                      count: 12
                      percentage: 70.6
        '404':
          description: 任务不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    DefectTree:
      type: object
      description: 缺陷树形结构
      properties:
        totalCount:
          type: integer
          description: 缺陷总数
          example: 17
        vulnerabilityTypes:
          type: array
          description: 漏洞类型分组列表
          items:
            $ref: '#/components/schemas/VulnerabilityTypeGroup'

    VulnerabilityTypeGroup:
      type: object
      description: 漏洞类型分组
      properties:
        typeName:
          type: string
          description: 漏洞类型名称
          example: "SQL注入"
        typeCount:
          type: integer
          description: 该类型缺陷数量
          example: 5
        defects:
          type: array
          description: 缺陷列表
          items:
            $ref: '#/components/schemas/DefectTreeItem'

    DefectTreeItem:
      type: object
      description: 缺陷树节点项
      properties:
        defectId:
          type: integer
          format: int64
          description: 缺陷ID
          example: 1
        sequenceNumber:
          type: integer
          description: 序号
          example: 1
        defectPath:
          type: string
          description: 缺陷对象路径
          example: "VulnCore/JDBCAttack/src/main/java/hmy/BMIAttack.java"
        defectLine:
          type: integer
          description: 缺陷行号
          example: 22
        defectLevel:
          type: string
          description: 危险程度
          enum: ["高危", "中危", "低危"]
          example: "高危"
        verificationResult:
          type: string
          description: 验证结果
          enum: ["验证成功", "验证失败", "未验证"]
          example: "验证成功"

    ErrorResponse:
      type: object
      description: 错误响应
      required:
        - code
        - msg
      properties:
        code:
          type: integer
          description: 错误码
          example: 500
        msg:
          type: string
          description: 错误消息
          example: "操作失败"
        data:
          type: object
          nullable: true
          description: 错误详情
          example: null

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: 任务缺陷管理
    description: 任务缺陷树形结构管理相关接口
