# 模型配置管理系统接口文档

## 概述

模型配置管理系统提供完整的大模型配置管理功能，包括模型列表查询、新增配置、编辑更新、删除管理、连接测试、启用禁用等核心功能。

## 原型图功能分析

### 页面布局
- **标题**: 模型配置
- **搜索框**: 请输入模型名称
- **操作按钮**: 
  - 新增模型 (蓝色按钮)
  - 仅查看 (切换按钮)
  - 仅显示启用 (切换按钮)

### 列表字段
| 字段 | 示例值 | 说明 |
|------|--------|------|
| 序号 | 01, 02, 03 | 自动生成的序列号 |
| 模型名称 | GPT-4 通用模型 | 模型的显示名称 |
| 组织商 | OpenAI | 模型提供商 |
| 模型标识 | gpt-4-turbo | API调用标识 |
| 收费设置 | 30%, 20%, 50% | 权重设置进度条 |
| 模型当前状态 | 已启用/已禁用 | 状态标签 |
| 更新时间 | 2024-03-12 12:00:00 | 最后修改时间 |
| 操作 | 编辑、删除 | 操作按钮 |

## 核心接口

### 1. 模型配置列表查询
**GET** `/api/models`

分页查询模型配置信息列表，支持搜索和筛选。

**查询参数**:
- `current`: 当前页码 (默认1)
- `size`: 每页记录数 (默认10)
- `keyword`: 搜索关键词（模型名称）
- `supplier`: 供应商筛选
- `testStatus`: 测试状态筛选
- `enabledOnly`: 仅显示启用的模型

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "records": [
      {
        "id": 1,
        "sequenceNumber": "01",
        "modelName": "GPT-4 通用模型",
        "supplier": "OpenAI",
        "modelIdentifier": "gpt-4-turbo",
        "weightSetting": 30,
        "currentStatus": "已启用",
        "testStatus": "正常",
        "modifyTime": "2024-03-12 12:00:00"
      }
    ],
    "total": 3,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 新增模型配置
**POST** `/api/models`

创建新的模型配置。

**请求参数**:
```json
{
  "modelName": "GPT-4 通用模型",
  "supplier": "OpenAI",
  "modelIdentifier": "gpt-4-turbo",
  "apiType": "REST",
  "baseUrl": "https://api.openai.com/v1",
  "apiKey": "sk-xxxxxxxxxxxxxxxx",
  "maxTokens": 4000,
  "temperature": 0.7,
  "timeoutSetting": 30,
  "weightSetting": 0.3,
  "extraHeaders": {}
}
```

### 3. 模型配置详情
**GET** `/api/models/{modelId}`

获取指定模型的详细配置信息。

### 4. 更新模型配置
**PUT** `/api/models/{modelId}`

更新指定模型的配置信息。

### 5. 删除模型配置
**DELETE** `/api/models/{modelId}`

删除指定的模型配置（软删除）。

## 模型操作接口

### 1. 测试模型连接
**POST** `/api/models/{modelId}/test`

测试指定模型的API连接是否正常。

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "testResult": "正常",
    "testMessage": "模型连接正常，响应时间: 1.2秒",
    "responseTime": 1.2,
    "testTime": "2024-03-12T12:00:00"
  }
}
```

### 2. 启用模型
**POST** `/api/models/{modelId}/enable`

启用指定的模型配置。

### 3. 禁用模型
**POST** `/api/models/{modelId}/disable`

禁用指定的模型配置。

## 数据模型

### 数据库字段映射

| 原型图字段 | 数据库字段 | 类型 | 说明 |
|-----------|-----------|------|------|
| 序号 | - | - | 前端生成的序列号 |
| 模型名称 | model_name | varchar(255) | 模型显示名称 |
| 组织商 | supplier | varchar(255) | 模型提供商 |
| 模型标识 | model_identifier | varchar(255) | API调用标识 |
| 收费设置 | weight_setting | numeric(3,2) | 权重设置(0.00-1.00) |
| 模型当前状态 | - | - | 基于test_status计算 |
| 更新时间 | modify_time | timestamp | 最后修改时间 |

### 状态映射逻辑
```javascript
// 模型当前状态计算逻辑
const getCurrentStatus = (testStatus, delFlag) => {
  if (delFlag === 1) return "已删除";
  if (testStatus === "正常") return "已启用";
  if (testStatus === "异常") return "已禁用";
  return "未测试";
};
```

### 收费设置显示
```javascript
// 权重设置转换为百分比显示
const getWeightPercentage = (weightSetting) => {
  return Math.round(weightSetting * 100); // 0.3 -> 30%
};
```

## 业务规则

### 1. 模型配置规则
- 模型名称在同一供应商下不能重复
- 模型标识必须唯一
- API密钥必须有效
- 权重设置范围: 0.00-1.00
- 温度设置范围: 0.00-1.00

### 2. 状态管理规则
- 新创建的模型默认状态为"未测试"
- 测试通过后状态变为"正常"
- 测试失败后状态变为"异常"
- 只有状态为"正常"的模型才能被任务使用

### 3. 权重分配规则
- 所有启用模型的权重总和应该为1.0
- 权重决定模型在任务中的使用比例
- 权重为0的模型不参与任务分配

### 4. 删除规则
- 正在被任务使用的模型不能删除
- 删除操作为软删除，设置del_flag=1
- 软删除的模型不显示在列表中

## 前端实现建议

### 1. 列表展示
```javascript
// 权重设置进度条组件
const WeightProgress = ({ weight }) => (
  <Progress 
    percent={weight * 100} 
    size="small"
    strokeColor="#1890ff"
    showInfo={true}
    format={percent => `${percent}%`}
  />
);

// 状态标签组件
const StatusTag = ({ status }) => {
  const colorMap = {
    '已启用': 'green',
    '已禁用': 'red',
    '未测试': 'orange'
  };
  return <Tag color={colorMap[status]}>{status}</Tag>;
};
```

### 2. 搜索筛选
```javascript
// 搜索表单
const SearchForm = () => (
  <Form layout="inline">
    <Form.Item name="keyword">
      <Input placeholder="请输入模型名称" />
    </Form.Item>
    <Form.Item name="supplier">
      <Select placeholder="选择供应商">
        <Option value="OpenAI">OpenAI</Option>
        <Option value="百度">百度</Option>
        <Option value="Anthropic">Anthropic</Option>
      </Select>
    </Form.Item>
    <Form.Item name="enabledOnly">
      <Switch checkedChildren="仅显示启用" />
    </Form.Item>
  </Form>
);
```

### 3. 操作按钮
```javascript
// 操作列组件
const ActionColumn = ({ record }) => (
  <Space>
    <Button 
      type="link" 
      icon={<EditOutlined />}
      onClick={() => handleEdit(record.id)}
    >
      编辑
    </Button>
    <Button 
      type="link" 
      icon={<TestOutlined />}
      onClick={() => handleTest(record.id)}
    >
      测试
    </Button>
    <Popconfirm 
      title="确定删除此模型配置吗？"
      onConfirm={() => handleDelete(record.id)}
    >
      <Button 
        type="link" 
        danger
        icon={<DeleteOutlined />}
      >
        删除
      </Button>
    </Popconfirm>
  </Space>
);
```

## 性能优化

### 1. 数据库优化
- 在supplier、test_status字段上建立索引
- 模型配置数据使用缓存，减少数据库查询
- 分页查询使用limit/offset优化

### 2. 接口优化
- 列表查询支持字段选择，减少数据传输
- 模型测试使用异步处理，避免阻塞
- 批量操作使用事务保证数据一致性

### 3. 前端优化
- 使用防抖处理搜索输入
- 模型测试显示进度条和状态
- 表格使用虚拟滚动处理大数据量

## 安全考虑

### 1. API密钥安全
- API密钥在数据库中加密存储
- 前端显示时进行脱敏处理
- 只有管理员权限才能查看完整密钥

### 2. 权限控制
- 模型配置管理需要管理员权限
- 普通用户只能查看已启用的模型
- 操作日志记录所有配置变更

### 3. 数据验证
- 严格验证API密钥格式
- 验证URL格式和可访问性
- 参数范围检查和类型验证

## 扩展功能

### 1. 模型监控
- 实时监控模型API状态
- 统计模型使用频率和成功率
- 自动故障切换机制

### 2. 成本管理
- 记录模型调用次数和费用
- 设置使用配额和预警
- 生成成本分析报告

### 3. 版本管理
- 支持模型配置版本控制
- 配置变更历史记录
- 支持配置回滚功能

## 错误处理

### 常见错误场景
- 模型名称重复 (400)
- API密钥无效 (400)
- 模型连接超时 (500)
- 权限不足 (403)

### 错误响应示例
```json
{
  "code": 400,
  "msg": "模型名称已存在",
  "data": null
}
```

## 总结

模型配置管理系统提供了完整的大模型配置管理功能，支持：

1. **完整的CRUD操作**: 创建、查询、更新、删除模型配置
2. **灵活的搜索筛选**: 支持多维度搜索和筛选
3. **实时状态管理**: 模型连接测试和状态监控
4. **权重分配机制**: 支持模型使用权重配置
5. **安全性保障**: API密钥加密和权限控制
6. **良好的扩展性**: 支持后续功能扩展

接口设计遵循RESTful规范，响应格式统一，便于前端开发和后续维护。
