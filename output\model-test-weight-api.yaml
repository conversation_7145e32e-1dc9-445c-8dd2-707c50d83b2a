openapi: 3.0.3
info:
  title: 模型测试和权重配置API
  description: 模型测试功能和权重配置管理接口
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.example.com
    description: 生产环境

paths:
  /models/available:
    get:
      tags:
        - 模型测试
      summary: 获取可用模型列表
      description: 获取所有可用于测试的模型列表（用于模型测试弹窗的下拉选择）
      operationId: getAvailableModels
      responses:
        '200':
          description: 成功获取可用模型列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        modelId:
                          type: integer
                          format: int64
                          description: 模型ID
                          example: 1
                        modelName:
                          type: string
                          description: 模型名称
                          example: "GPT-4 通用模型"
                        supplier:
                          type: string
                          description: 供应商
                          example: "OpenAI"
                        modelIdentifier:
                          type: string
                          description: 模型标识
                          example: "gpt-4-turbo"
                        status:
                          type: string
                          description: 模型状态
                          enum: ["正常", "异常", "未测试"]
                          example: "正常"
              example:
                code: 0
                msg: "string"
                data:
                  - modelId: 1
                    modelName: "GPT-4 通用模型"
                    supplier: "OpenAI"
                    modelIdentifier: "gpt-4-turbo"
                    status: "正常"
                  - modelId: 2
                    modelName: "文心一言安全版"
                    supplier: "百度"
                    modelIdentifier: "ERNIE-Bot-Security"
                    status: "未测试"
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /models/test:
    post:
      tags:
        - 模型测试
      summary: 执行模型测试
      description: 对指定模型进行功能测试，包含自定义测试内容
      operationId: executeModelTest
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - modelId
                - testContent
              properties:
                modelId:
                  type: integer
                  format: int64
                  description: 选择的模型ID
                  example: 1
                testContent:
                  type: string
                  description: 测试输入内容
                  example: "请输入测试内容，例如一段代码，用户输入或系统目标，用于模型检测..."
            example:
              modelId: 1
              testContent: "请输入测试内容，例如一段代码，用户输入或系统目标，用于模型检测..."
      responses:
        '200':
          description: 模型测试执行成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      testId:
                        type: string
                        description: 测试任务ID
                        example: "test_20240312_001"
                      modelId:
                        type: integer
                        format: int64
                        description: 模型ID
                        example: 1
                      modelName:
                        type: string
                        description: 模型名称
                        example: "GPT-4 通用模型"
                      testContent:
                        type: string
                        description: 测试输入内容
                        example: "请输入测试内容..."
                      testOutput:
                        type: string
                        description: 模型输出结果
                        example: "模型输出将显示在这里"
                      responseTime:
                        type: number
                        format: float
                        description: 响应时间（秒）
                        example: 2.5
                      tokenUsage:
                        type: integer
                        description: Token消耗
                        example: 150
                      testStatus:
                        type: string
                        description: 测试状态
                        enum: ["成功", "失败", "进行中"]
                        example: "成功"
                      testTime:
                        type: string
                        format: date-time
                        description: 测试时间
                        example: "2024-03-12T12:00:00"
                      errorMessage:
                        type: string
                        description: 错误信息（如果测试失败）
                        nullable: true
                        example: null
              example:
                code: 0
                msg: "string"
                data:
                  testId: "test_20240312_001"
                  modelId: 1
                  modelName: "GPT-4 通用模型"
                  testContent: "请输入测试内容..."
                  testOutput: "模型输出将显示在这里"
                  responseTime: 2.5
                  tokenUsage: 150
                  testStatus: "成功"
                  testTime: "2024-03-12T12:00:00"
                  errorMessage: null
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 模型不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /models/weights:
    get:
      tags:
        - 权重配置
      summary: 获取模型权重配置
      description: 获取当前所有模型的权重配置信息
      operationId: getModelWeights
      responses:
        '200':
          description: 成功获取模型权重配置
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      models:
                        type: array
                        description: 模型权重列表
                        items:
                          $ref: '#/components/schemas/ModelWeight'
                      totalWeight:
                        type: number
                        format: float
                        description: 当前总权重
                        example: 100
                      isValid:
                        type: boolean
                        description: 权重配置是否有效（总和是否为100%）
                        example: true
              example:
                code: 0
                msg: "string"
                data:
                  models:
                    - modelId: 1
                      modelName: "GPT-4 漏洞检测"
                      currentWeight: 30
                      maxWeight: 100
                      minWeight: 0
                      status: "已启用"
                    - modelId: 2
                      modelName: "文心一言安全版"
                      currentWeight: 20
                      maxWeight: 100
                      minWeight: 0
                      status: "已启用"
                    - modelId: 3
                      modelName: "Claude-3"
                      currentWeight: 50
                      maxWeight: 100
                      minWeight: 0
                      status: "已启用"
                  totalWeight: 100
                  isValid: true
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - 权重配置
      summary: 更新模型权重配置
      description: 批量更新模型权重配置，支持平均分配功能
      operationId: updateModelWeights
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - weights
              properties:
                weights:
                  type: array
                  description: 模型权重配置列表
                  items:
                    type: object
                    required:
                      - modelId
                      - weight
                    properties:
                      modelId:
                        type: integer
                        format: int64
                        description: 模型ID
                        example: 1
                      weight:
                        type: number
                        format: float
                        description: 权重百分比 (0-100)
                        minimum: 0
                        maximum: 100
                        example: 30
                autoBalance:
                  type: boolean
                  description: 是否自动平均分配权重
                  default: false
                  example: false
            example:
              weights:
                - modelId: 1
                  weight: 30
                - modelId: 2
                  weight: 20
                - modelId: 3
                  weight: 50
              autoBalance: false
      responses:
        '200':
          description: 权重配置更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      updatedModels:
                        type: array
                        description: 更新后的模型权重
                        items:
                          $ref: '#/components/schemas/ModelWeight'
                      totalWeight:
                        type: number
                        format: float
                        description: 更新后的总权重
                        example: 100
              example:
                code: 0
                msg: "string"
                data:
                  updatedModels:
                    - modelId: 1
                      modelName: "GPT-4 漏洞检测"
                      currentWeight: 30
                      maxWeight: 100
                      minWeight: 0
                      status: "已启用"
                  totalWeight: 100
        '400':
          description: 请求参数错误（如权重总和不为100%）
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /models/weights/auto-balance:
    post:
      tags:
        - 权重配置
      summary: 自动平均分配权重
      description: 对所有启用的模型进行平均权重分配
      operationId: autoBalanceWeights
      responses:
        '200':
          description: 自动分配权重成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      balancedModels:
                        type: array
                        description: 平均分配后的模型权重
                        items:
                          $ref: '#/components/schemas/ModelWeight'
                      averageWeight:
                        type: number
                        format: float
                        description: 平均权重值
                        example: 33.33
              example:
                code: 0
                msg: "string"
                data:
                  balancedModels:
                    - modelId: 1
                      modelName: "GPT-4 漏洞检测"
                      currentWeight: 33.33
                      maxWeight: 100
                      minWeight: 0
                      status: "已启用"
                  averageWeight: 33.33
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    ModelWeight:
      type: object
      description: 模型权重配置
      properties:
        modelId:
          type: integer
          format: int64
          description: 模型ID
          example: 1
        modelName:
          type: string
          description: 模型名称
          example: "GPT-4 漏洞检测"
        currentWeight:
          type: number
          format: float
          description: 当前权重百分比
          minimum: 0
          maximum: 100
          example: 30
        maxWeight:
          type: number
          format: float
          description: 最大权重限制
          example: 100
        minWeight:
          type: number
          format: float
          description: 最小权重限制
          example: 0
        status:
          type: string
          description: 模型状态
          enum: ["已启用", "已禁用"]
          example: "已启用"

    ModelTestResult:
      type: object
      description: 模型测试结果
      properties:
        testId:
          type: string
          description: 测试任务ID
          example: "test_20240312_001"
        modelId:
          type: integer
          format: int64
          description: 模型ID
          example: 1
        modelName:
          type: string
          description: 模型名称
          example: "GPT-4 通用模型"
        testContent:
          type: string
          description: 测试输入内容
          example: "请输入测试内容..."
        testOutput:
          type: string
          description: 模型输出结果
          example: "模型输出将显示在这里"
        responseTime:
          type: number
          format: float
          description: 响应时间（秒）
          example: 2.5
        tokenUsage:
          type: integer
          description: Token消耗
          example: 150
        testStatus:
          type: string
          description: 测试状态
          enum: ["成功", "失败", "进行中"]
          example: "成功"
        testTime:
          type: string
          format: date-time
          description: 测试时间
          example: "2024-03-12T12:00:00"
        errorMessage:
          type: string
          description: 错误信息（如果测试失败）
          nullable: true
          example: null

    AvailableModel:
      type: object
      description: 可用模型信息
      properties:
        modelId:
          type: integer
          format: int64
          description: 模型ID
          example: 1
        modelName:
          type: string
          description: 模型名称
          example: "GPT-4 通用模型"
        supplier:
          type: string
          description: 供应商
          example: "OpenAI"
        modelIdentifier:
          type: string
          description: 模型标识
          example: "gpt-4-turbo"
        status:
          type: string
          description: 模型状态
          enum: ["正常", "异常", "未测试"]
          example: "正常"

    ErrorResponse:
      type: object
      description: 错误响应
      required:
        - code
        - msg
      properties:
        code:
          type: integer
          description: 错误码
          example: 500
        msg:
          type: string
          description: 错误消息
          example: "操作失败"
        data:
          type: object
          nullable: true
          description: 错误详情
          example: null

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: 模型测试
    description: 模型测试功能相关接口
  - name: 权重配置
    description: 模型权重配置管理相关接口
