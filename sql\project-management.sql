-- auto-generated definition
create table app_sast_project_management
(
    id                  bigint       default snow_next_id_bigint()         not null
        primary key,
    project_name        varchar(255)                                       not null,
    created_id          bigint                                             not null,
    created_name        varchar(50)                                        not null,
    created_time        timestamp    default now(),
    modify_id           bigint,
    modify_name         varchar(50),
    modify_time         timestamp    default now(),
    del_flag            smallint     default '0'::smallint                 not null,
    project_description varchar(255) default '项目描述'::character varying not null
);

comment on table app_sast_project_management is 'SAST项目管理表';

comment on column app_sast_project_management.id is '自增主键ID';

comment on column app_sast_project_management.project_name is '项目名称';

comment on column app_sast_project_management.created_id is '创建人标识';

comment on column app_sast_project_management.created_name is '创建人';

comment on column app_sast_project_management.created_time is '创建时间';

comment on column app_sast_project_management.modify_id is '更新人标识';

comment on column app_sast_project_management.modify_name is '更新人';

comment on column app_sast_project_management.modify_time is '更新时间';

comment on column app_sast_project_management.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_project_management
    owner to postgres;

create index idx_app_sast_project_management_create_time
    on app_sast_project_management (created_time);

