-- auto-generated definition
create table app_sast_report_management
(
    id                     bigint       default snow_next_id_bigint() not null
        primary key,
    report_name            varchar(255)                               not null,
    project_id             bigint                                     not null
        constraint fk_report_project
            references app_sast_project_management,
    task_id                bigint                                     not null
        constraint fk_report_task
            references app_sast_task_management,
    report_generation_time timestamp(6) default CURRENT_TIMESTAMP(6),
    created_id             bigint                                     not null,
    created_name           varchar(50)                                not null,
    created_time           timestamp    default now(),
    modify_id              bigint,
    modify_name            varchar(50),
    modify_time            timestamp    default now(),
    del_flag               smallint     default 0                     not null
);

comment on table app_sast_report_management is 'SAST报告管理表';

comment on column app_sast_report_management.id is '主键ID，自增';

comment on column app_sast_report_management.report_name is '报告名称，必填';

comment on column app_sast_report_management.project_id is '所属项目ID，外键关联';

comment on column app_sast_report_management.task_id is '所属任务ID，外键关联';

comment on column app_sast_report_management.report_generation_time is '报告生成时间，精确到微秒';

comment on column app_sast_report_management.created_id is '创建人ID';

comment on column app_sast_report_management.created_name is '创建人姓名';

comment on column app_sast_report_management.created_time is '创建时间';

comment on column app_sast_report_management.modify_id is '更新人ID';

comment on column app_sast_report_management.modify_name is '更新人姓名';

comment on column app_sast_report_management.modify_time is '更新时间';

comment on column app_sast_report_management.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_report_management
    owner to postgres;

create index idx_app_sast_report_management_project_id
    on app_sast_report_management (project_id);

create index idx_app_sast_report_management_task_id
    on app_sast_report_management (task_id);


'[
        {
            "model": "DeepSeek-R1",
            "reason":"污染源来自 Web API 的请求体参数属于用户输入污染汇为 Groovy 脚本的动态执行可能导致任意代码执行该路径构成 CWE-094 漏洞存在 RCE 风险"
        },
         {
            "model": "Qwen-2.5-coder-instruct",
            "reason":"当数据库查询返回的未经清洗的数据被直接拼接进 JavaScript 的Function构造函数中用于动态生成代码时，就形成了一条隐蔽的风险路径。"
        }
]'