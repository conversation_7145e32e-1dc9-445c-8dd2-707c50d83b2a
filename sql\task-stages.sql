-- auto-generated definition
create table app_sast_task_stages
(
    id                 bigint    default snow_next_id_bigint() not null
        primary key,
    task_id            bigint                                  not null
        constraint fk_task_stages_task
            references app_sast_task_management,
    stage_name         varchar(100)                            not null,
    stage_status       varchar(50)                             not null,
    start_time         timestamp(6),
    end_time           timestamp(6),
    stage_order        integer                                 not null,
    token              integer,
    vulnerability_type varchar(100),
    created_id         bigint                                  not null,
    created_name       varchar(50)                             not null,
    created_time       timestamp default now(),
    modify_id          bigint,
    modify_name        varchar(50),
    modify_time        timestamp default now(),
    del_flag           smallint  default 0                     not null
);

comment on table app_sast_task_stages is 'SAST任务执行阶段表';

comment on column app_sast_task_stages.id is '主键ID';

comment on column app_sast_task_stages.task_id is '关联任务ID，外键关联';

comment on column app_sast_task_stages.stage_name is '阶段名称，必填';

comment on column app_sast_task_stages.stage_status is '阶段状态：进行中/已完成/失败';

comment on column app_sast_task_stages.start_time is '开始时间，精确到微秒';

comment on column app_sast_task_stages.end_time is '结束时间，精确到微秒';

comment on column app_sast_task_stages.stage_order is '阶段顺序，必填';

comment on column app_sast_task_stages.token is 'token消耗数，未统计填-1';

comment on column app_sast_task_stages.vulnerability_type is '漏洞类型，某些阶段与特定漏洞类型相关';

comment on column app_sast_task_stages.created_id is '创建人ID';

comment on column app_sast_task_stages.created_name is '创建人姓名';

comment on column app_sast_task_stages.created_time is '创建时间';

comment on column app_sast_task_stages.modify_id is '更新人ID';

comment on column app_sast_task_stages.modify_name is '更新人姓名';

comment on column app_sast_task_stages.modify_time is '更新时间';

comment on column app_sast_task_stages.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_task_stages
    owner to postgres;

create index idx_app_sast_task_stages_task_id
    on app_sast_task_stages (task_id);

create index idx_app_sast_task_stages_stage_status
    on app_sast_task_stages (stage_status);

create index idx_app_sast_task_stages_stage_order
    on app_sast_task_stages (stage_order);

