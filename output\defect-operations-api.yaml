openapi: 3.0.3
info:
  title: 缺陷操作管理API
  description: SAST缺陷验证和批量操作相关接口文档
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.example.com
    description: 生产环境

paths:
  /defects/{defectId}/verify:
    post:
      tags:
        - 缺陷验证
      summary: 验证缺陷
      description: 对指定缺陷进行验证，更新验证结果
      operationId: verifyDefect
      parameters:
        - name: defectId
          in: path
          description: 缺陷ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - verificationResult
              properties:
                verificationResult:
                  type: string
                  description: 验证结果
                  enum: ["验证成功", "验证失败"]
                  example: "验证成功"
                verificationNote:
                  type: string
                  description: 验证说明
                  example: "通过POC验证确认存在SQL注入漏洞"
      responses:
        '200':
          description: 验证成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      defectId:
                        type: integer
                        format: int64
                        description: 缺陷ID
                        example: 1
                      verificationResult:
                        type: string
                        description: 验证结果
                        example: "验证成功"
                      verificationTime:
                        type: string
                        format: date-time
                        description: 验证时间
                        example: "2025-01-29T14:30:00"
              example:
                code: 0
                msg: "string"
                data:
                  defectId: 1
                  verificationResult: "验证成功"
                  verificationTime: "2025-01-29T14:30:00"
        '404':
          description: 缺陷不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /defects/batch-verify:
    post:
      tags:
        - 缺陷验证
      summary: 批量验证缺陷
      description: 批量对多个缺陷进行验证
      operationId: batchVerifyDefects
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - defectIds
                - verificationResult
              properties:
                defectIds:
                  type: array
                  description: 缺陷ID列表
                  items:
                    type: integer
                    format: int64
                  example: [1, 2, 3, 4, 5]
                verificationResult:
                  type: string
                  description: 验证结果
                  enum: ["验证成功", "验证失败"]
                  example: "验证成功"
                verificationNote:
                  type: string
                  description: 验证说明
                  example: "批量验证操作"
      responses:
        '200':
          description: 批量验证成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      successCount:
                        type: integer
                        description: 成功验证数量
                        example: 5
                      failCount:
                        type: integer
                        description: 失败验证数量
                        example: 0
                      totalCount:
                        type: integer
                        description: 总数量
                        example: 5
              example:
                code: 0
                msg: "string"
                data:
                  successCount: 5
                  failCount: 0
                  totalCount: 5
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /defects/batch-fix:
    post:
      tags:
        - 缺陷操作
      summary: 批量修复缺陷
      description: 批量标记多个缺陷为已修复状态
      operationId: batchFixDefects
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - defectIds
              properties:
                defectIds:
                  type: array
                  description: 缺陷ID列表
                  items:
                    type: integer
                    format: int64
                  example: [1, 2, 3, 4, 5]
                fixNote:
                  type: string
                  description: 修复说明
                  example: "批量修复操作"
      responses:
        '200':
          description: 批量修复成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      successCount:
                        type: integer
                        description: 成功修复数量
                        example: 5
                      failCount:
                        type: integer
                        description: 失败修复数量
                        example: 0
                      totalCount:
                        type: integer
                        description: 总数量
                        example: 5
              example:
                code: 0
                msg: "string"
                data:
                  successCount: 5
                  failCount: 0
                  totalCount: 5
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /defects/export:
    post:
      tags:
        - 缺陷操作
      summary: 导出缺陷报告
      description: 导出缺陷信息为Excel或PDF格式
      operationId: exportDefects
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - format
              properties:
                format:
                  type: string
                  description: 导出格式
                  enum: ["excel", "pdf"]
                  example: "excel"
                defectIds:
                  type: array
                  description: 指定导出的缺陷ID列表（可选，不传则导出所有）
                  items:
                    type: integer
                    format: int64
                  example: [1, 2, 3, 4, 5]
                filters:
                  type: object
                  description: 导出筛选条件
                  properties:
                    projectId:
                      type: integer
                      format: int64
                      description: 项目ID
                      example: 1
                    taskId:
                      type: integer
                      format: int64
                      description: 任务ID
                      example: 123456
                    defectLevel:
                      type: string
                      description: 危险程度
                      enum: ["高危", "中危", "低危"]
                      example: "高危"
                    verificationResult:
                      type: string
                      description: 验证结果
                      enum: ["验证成功", "验证失败", "未验证"]
                      example: "验证成功"
      responses:
        '200':
          description: 导出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      downloadUrl:
                        type: string
                        description: 下载链接
                        example: "https://api.example.com/downloads/defect-report-20250129.xlsx"
                      fileName:
                        type: string
                        description: 文件名
                        example: "defect-report-20250129.xlsx"
                      fileSize:
                        type: integer
                        format: int64
                        description: 文件大小（字节）
                        example: 2048576
              example:
                code: 0
                msg: "string"
                data:
                  downloadUrl: "https://api.example.com/downloads/defect-report-20250129.xlsx"
                  fileName: "defect-report-20250129.xlsx"
                  fileSize: 2048576
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /system/vulnerability-types:
    get:
      tags:
        - 系统配置
      summary: 获取漏洞类型列表
      description: 获取支持的漏洞类型选项列表
      operationId: getVulnerabilityTypes
      responses:
        '200':
          description: 成功获取漏洞类型列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        value:
                          type: string
                          description: 漏洞类型值
                          example: "SQL注入"
                        label:
                          type: string
                          description: 显示标签
                          example: "SQL注入"
              example:
                code: 0
                msg: "string"
                data:
                  - value: "SQL注入"
                    label: "SQL注入"
                  - value: "RCE"
                    label: "远程代码执行"
                  - value: "XSS"
                    label: "跨站脚本攻击"
                  - value: "CSRF"
                    label: "跨站请求伪造"

components:
  schemas:
    ErrorResponse:
      type: object
      description: 错误响应
      required:
        - code
        - msg
      properties:
        code:
          type: integer
          description: 错误码
          example: 500
        msg:
          type: string
          description: 错误消息
          example: "操作失败"
        data:
          type: object
          nullable: true
          description: 错误详情
          example: null

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: 缺陷验证
    description: 缺陷验证相关接口
  - name: 缺陷操作
    description: 缺陷批量操作相关接口
  - name: 系统配置
    description: 系统配置选项相关接口
