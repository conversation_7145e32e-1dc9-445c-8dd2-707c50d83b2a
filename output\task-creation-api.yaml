openapi: 3.0.3
info:
  title: 任务创建管理API
  description: SAST任务创建相关接口文档
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.example.com
    description: 生产环境

paths:
  /project-management/{projectId}/tasks:
    post:
      tags:
        - 任务管理
      summary: 创建新任务（第一步）
      description: 创建新的SAST任务，提交任务基本信息
      operationId: createTask
      parameters:
        - name: projectId
          in: path
          description: 项目ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTaskRequest'
            example:
              taskName: "struts检测"
              taskLanguage: "Java"
              analysisMethod: "离线分析"
              buildMethod: "Java-maven"
              taskDescription: "对struts框架进行安全检测分析"
      responses:
        '200':
          description: 任务创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      taskId:
                        type: integer
                        format: int64
                        description: 创建的任务ID
                        example: 123456
                      taskName:
                        type: string
                        description: 任务名称
                        example: "struts检测"
                      taskStatus:
                        type: string
                        description: 任务状态
                        example: "排队"
              example:
                code: 0
                msg: "string"
                data:
                  taskId: 123456
                  taskName: "struts检测"
                  taskStatus: "排队"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /tasks/{taskId}/files/upload:
    post:
      tags:
        - 任务管理
      summary: 上传任务文件（第二步）
      description: 为指定任务上传源代码文件或预编译文件
      operationId: uploadTaskFile
      parameters:
        - name: taskId
          in: path
          description: 任务ID
          required: true
          schema:
            type: integer
            format: int64
            example: 123456
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                fileType:
                  type: string
                  description: 文件类型
                  enum: ["源码文件", "预编译压缩包"]
                  example: "源码文件"
                file:
                  type: string
                  format: binary
                  description: 上传的文件
              required:
                - fileType
                - file
      responses:
        '200':
          description: 文件上传成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      fileId:
                        type: integer
                        format: int64
                        description: 文件ID
                        example: 789
                      fileName:
                        type: string
                        description: 文件名
                        example: "xxx模型源码编译文件.xlsx"
                      fileSize:
                        type: integer
                        format: int64
                        description: 文件大小（字节）
                        example: 1048576
                      uploadTime:
                        type: string
                        format: date-time
                        description: 上传时间
                        example: "2025-01-29T10:30:00"
              example:
                code: 0
                msg: "string"
                data:
                  fileId: 789
                  fileName: "xxx模型源码编译文件.xlsx"
                  fileSize: 1048576
                  uploadTime: "2025-01-29T10:30:00"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /system/programming-languages:
    get:
      tags:
        - 系统配置
      summary: 获取编程语言列表
      description: 获取支持的编程语言选项列表
      operationId: getProgrammingLanguages
      responses:
        '200':
          description: 成功获取编程语言列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        value:
                          type: string
                          description: 语言值
                          example: "Java"
                        label:
                          type: string
                          description: 显示标签
                          example: "Java"
              example:
                code: 0
                msg: "string"
                data:
                  - value: "Java"
                    label: "Java"
                  - value: "Python"
                    label: "Python"
                  - value: "Go"
                    label: "Go"
                  - value: "C++"
                    label: "C++"

  /system/analysis-methods:
    get:
      tags:
        - 系统配置
      summary: 获取分析方式列表
      description: 获取支持的分析方式选项列表
      operationId: getAnalysisMethods
      responses:
        '200':
          description: 成功获取分析方式列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        value:
                          type: string
                          description: 分析方式值
                          example: "离线分析"
                        label:
                          type: string
                          description: 显示标签
                          example: "离线分析"
              example:
                code: 0
                msg: "string"
                data:
                  - value: "离线分析"
                    label: "离线分析"
                  - value: "在线分析"
                    label: "在线分析"

  /system/build-methods:
    get:
      tags:
        - 系统配置
      summary: 获取构建方法列表
      description: 获取支持的构建方法选项列表
      operationId: getBuildMethods
      responses:
        '200':
          description: 成功获取构建方法列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        value:
                          type: string
                          description: 构建方法值
                          example: "Java-maven"
                        label:
                          type: string
                          description: 显示标签
                          example: "Java-maven"
              example:
                code: 0
                msg: "string"
                data:
                  - value: "Java-maven"
                    label: "Java-maven"
                  - value: "Java-gradle"
                    label: "Java-gradle"
                  - value: "Python-pip"
                    label: "Python-pip"

components:
  schemas:
    CreateTaskRequest:
      type: object
      description: 创建任务请求
      required:
        - taskName
        - taskLanguage
        - analysisMethod
      properties:
        taskName:
          type: string
          description: 任务名称
          maxLength: 255
          example: "struts检测"
        taskLanguage:
          type: string
          description: 编程语言
          maxLength: 100
          example: "Java"
        analysisMethod:
          type: string
          description: 分析方式
          example: "离线分析"
        buildMethod:
          type: string
          description: 构建方法（在线分析时必填）
          maxLength: 100
          example: "Java-maven"
        taskDescription:
          type: string
          description: 任务描述
          example: "对struts框架进行安全检测分析"

    ErrorResponse:
      type: object
      description: 错误响应
      required:
        - code
        - msg
      properties:
        code:
          type: integer
          description: 错误码
          example: 500
        msg:
          type: string
          description: 错误消息
          example: "操作失败"
        data:
          type: object
          nullable: true
          description: 错误详情
          example: null

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: 任务管理
    description: 任务创建和管理相关接口
  - name: 系统配置
    description: 系统配置选项相关接口
