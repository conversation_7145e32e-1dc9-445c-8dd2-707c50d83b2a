openapi: 3.0.3
info:
  title: 项目管理-任务列表API
  description: SAST项目管理系统-任务管理模块接口文档
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.example.com
    description: 生产环境

paths:
  /project-management/{projectId}/tasks:
    get:
      tags:
        - 项目管理
      summary: 获取项目任务列表
      description: 分页获取指定项目下的任务列表，包含任务基本信息、文件上传状态、分析状态、漏洞统计等信息
      operationId: getProjectTaskList
      parameters:
        - name: projectId
          in: path
          description: 项目ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
        - name: page
          in: query
          description: 页码，从1开始
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: size
          in: query
          description: 每页大小
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
        - name: keyword
          in: query
          description: 搜索关键词（支持任务名称、创建人模糊搜索）
          required: false
          schema:
            type: string
            maxLength: 255
            example: "测试任务"
      responses:
        '200':
          description: 成功获取项目任务列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 响应状态码
                    example: 0
                  msg:
                    type: string
                    description: 响应消息
                    example: "string"
                  data:
                    type: object
                    properties:
                      summary:
                        $ref: '#/components/schemas/TaskSummary'
                      taskList:
                        type: array
                        description: 任务列表数据
                        items:
                          $ref: '#/components/schemas/TaskListItem'
              examples:
                success:
                  summary: 成功响应示例
                  value:
                    code: 0
                    msg: "string"
                    data:
                      summary:
                        totalTasks: 265
                        highRisk: 140
                        highRiskPercent: 30
                        mediumRisk: 109
                        mediumRiskPercent: 30
                        lowRisk: 87
                        lowRiskPercent: 40
                      taskList:
                        - taskId: "TASK-20250723-001"
                          taskName: "测试智能化系统"
                          fileUploadStatus:
                            sourceFileProgress: 100
                            configFileProgress: 100
                          analysisStatus: "AI分析完成"
                          analysisResults:
                            highRisk: 12
                            mediumRisk: 8
                            lowRisk: 5
                          updateTime: "2024-03-12T12:00:00"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 400
                msg: "参数错误"
                data: null
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 500
                msg: "操作失败"
                data: null

components:
  schemas:
    TaskSummary:
      type: object
      description: 任务统计汇总
      properties:
        totalTasks:
          type: integer
          description: 测试总数
          example: 265
        highRisk:
          type: integer
          description: 高危漏洞总数
          example: 140
        highRiskPercent:
          type: integer
          description: 高危漏洞占比(%)
          example: 30
        mediumRisk:
          type: integer
          description: 中危漏洞总数
          example: 109
        mediumRiskPercent:
          type: integer
          description: 中危漏洞占比(%)
          example: 30
        lowRisk:
          type: integer
          description: 低危漏洞总数
          example: 87
        lowRiskPercent:
          type: integer
          description: 低危漏洞占比(%)
          example: 40

    TaskListItem:
      type: object
      description: 任务列表项
      required:
        - taskId
        - taskName
        - updateTime
      properties:
        taskId:
          type: string
          description: 任务ID
          example: "TASK-20250723-001"
        taskName:
          type: string
          description: 任务名称
          maxLength: 255
          example: "测试智能化系统"
        fileUploadStatus:
          $ref: '#/components/schemas/FileUploadStatus'
        analysisStatus:
          type: string
          description: 分析状态
          enum: ["排队", "传播器查找", "链路分析", "研判", "POC验证", "修复建议生成", "AI分析完成", "分析失败"]
          example: "AI分析完成"
        analysisResults:
          $ref: '#/components/schemas/AnalysisResults'
        updateTime:
          type: string
          format: date-time
          description: 更新时间
          example: "2024-03-12T12:00:00"

    FileUploadStatus:
      type: object
      description: 文件上传状态
      properties:
        sourceFileProgress:
          type: integer
          description: 源文件上传进度(%)
          minimum: 0
          maximum: 100
          example: 100
        configFileProgress:
          type: integer
          description: 环境配置文件上传进度(%)
          minimum: 0
          maximum: 100
          example: 100

    AnalysisResults:
      type: object
      description: 分析结果
      properties:
        highRisk:
          type: integer
          description: 高危漏洞数量
          minimum: 0
          example: 12
        mediumRisk:
          type: integer
          description: 中危漏洞数量
          minimum: 0
          example: 8
        lowRisk:
          type: integer
          description: 低危漏洞数量
          minimum: 0
          example: 5

    ErrorResponse:
      type: object
      description: 错误响应
      required:
        - code
        - msg
      properties:
        code:
          type: integer
          description: 错误码
          example: 500
        msg:
          type: string
          description: 错误消息
          example: "操作失败"
        data:
          type: object
          nullable: true
          description: 错误详情
          example: null

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: 项目管理
    description: 项目管理相关接口，包含项目列表、任务管理等功能
