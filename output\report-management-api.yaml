openapi: 3.0.3
info:
  title: 报告管理API
  description: SAST报告管理相关接口文档
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.example.com
    description: 生产环境

paths:
  /reports:
    get:
      tags:
        - 报告管理
      summary: 获取报告列表（分页）
      description: 分页查询报告信息列表，支持搜索和筛选
      operationId: getReportList
      parameters:
        - name: current
          in: query
          description: 当前页码
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: size
          in: query
          description: 每页记录数
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
        - name: keyword
          in: query
          description: 搜索关键词（项目名称）
          required: false
          schema:
            type: string
            example: "漏洞检测"
        - name: projectId
          in: query
          description: 项目ID筛选
          required: false
          schema:
            type: integer
            format: int64
            example: 1
        - name: taskId
          in: query
          description: 任务ID筛选
          required: false
          schema:
            type: integer
            format: int64
            example: 123456
      responses:
        '200':
          description: 成功获取报告列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      records:
                        type: array
                        items:
                          $ref: '#/components/schemas/ReportInfo'
                      total:
                        type: integer
                        description: 总记录数
                        example: 10
                      size:
                        type: integer
                        description: 每页记录数
                        example: 10
                      current:
                        type: integer
                        description: 当前页码
                        example: 1
                      pages:
                        type: integer
                        description: 总页数
                        example: 1
              example:
                code: 0
                msg: "string"
                data:
                  records:
                    - id: 1
                      sequenceNumber: "01"
                      reportName: "漏洞测试分析报告"
                      projectName: "漏洞检测测试"
                      projectId: 1
                      taskId: 123456
                      defectCount: 134
                      riskClassification:
                        highRisk: 100
                        mediumRisk: 30
                        lowRisk: 4
                      verificationSuccess: 130
                      testFailure: 4
                      reportGenerationTime: "2024-03-12 12:00:00"
                    - id: 2
                      sequenceNumber: "02"
                      reportName: "漏洞测试分析报告"
                      projectName: "漏洞检测测试"
                      projectId: 1
                      taskId: 123457
                      defectCount: 134
                      riskClassification:
                        highRisk: 100
                        mediumRisk: 30
                        lowRisk: 4
                      verificationSuccess: 130
                      testFailure: 4
                      reportGenerationTime: "2024-03-12 12:00:00"
                  total: 10
                  size: 10
                  current: 1
                  pages: 1
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      tags:
        - 报告管理
      summary: 生成报告
      description: 根据指定项目和任务生成新的分析报告
      operationId: generateReport
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - reportName
                - projectId
                - taskId
              properties:
                reportName:
                  type: string
                  description: 报告名称
                  maxLength: 255
                  example: "漏洞测试分析报告"
                projectId:
                  type: integer
                  format: int64
                  description: 项目ID
                  example: 1
                taskId:
                  type: integer
                  format: int64
                  description: 任务ID
                  example: 123456
                reportTemplate:
                  type: string
                  description: 报告模板类型
                  enum: ["标准模板", "详细模板", "简化模板"]
                  default: "标准模板"
                  example: "标准模板"
                includeCharts:
                  type: boolean
                  description: 是否包含图表
                  default: true
                  example: true
            example:
              reportName: "漏洞测试分析报告"
              projectId: 1
              taskId: 123456
              reportTemplate: "标准模板"
              includeCharts: true
      responses:
        '200':
          description: 报告生成成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      reportId:
                        type: integer
                        format: int64
                        description: 生成的报告ID
                        example: 11
                      reportName:
                        type: string
                        description: 报告名称
                        example: "漏洞测试分析报告"
                      generationTime:
                        type: string
                        description: 生成时间
                        example: "2024-03-12 12:00:00"
                      status:
                        type: string
                        description: 生成状态
                        enum: ["生成中", "生成成功", "生成失败"]
                        example: "生成成功"
              example:
                code: 0
                msg: "string"
                data:
                  reportId: 11
                  reportName: "漏洞测试分析报告"
                  generationTime: "2024-03-12 12:00:00"
                  status: "生成成功"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 项目或任务不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /reports/{reportId}:
    get:
      tags:
        - 报告管理
      summary: 查看报告详情
      description: 获取指定报告的详细信息和内容
      operationId: getReportDetail
      parameters:
        - name: reportId
          in: path
          description: 报告ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      responses:
        '200':
          description: 成功获取报告详情
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    $ref: '#/components/schemas/ReportDetail'
        '404':
          description: 报告不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      tags:
        - 报告管理
      summary: 删除报告
      description: 删除指定的报告（软删除）
      operationId: deleteReport
      parameters:
        - name: reportId
          in: path
          description: 报告ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      responses:
        '200':
          description: 报告删除成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    nullable: true
                    example: null
        '404':
          description: 报告不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /reports/{reportId}/download:
    get:
      tags:
        - 报告管理
      summary: 下载报告
      description: 下载指定报告的文件
      operationId: downloadReport
      parameters:
        - name: reportId
          in: path
          description: 报告ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
        - name: format
          in: query
          description: 下载格式
          required: false
          schema:
            type: string
            enum: ["pdf", "html", "docx"]
            default: "pdf"
            example: "pdf"
      responses:
        '200':
          description: 报告下载成功
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      downloadUrl:
                        type: string
                        description: 下载链接
                        example: "https://api.example.com/downloads/report-20240312.pdf"
                      fileName:
                        type: string
                        description: 文件名
                        example: "漏洞测试分析报告-20240312.pdf"
                      fileSize:
                        type: integer
                        format: int64
                        description: 文件大小（字节）
                        example: 2048576
        '404':
          description: 报告不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /reports/refresh:
    post:
      tags:
        - 报告管理
      summary: 数据刷新
      description: 刷新报告列表数据，重新计算统计信息
      operationId: refreshReportData
      responses:
        '200':
          description: 数据刷新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      refreshTime:
                        type: string
                        format: date-time
                        description: 刷新时间
                        example: "2024-03-12T12:00:00"
                      totalReports:
                        type: integer
                        description: 总报告数
                        example: 10
                      updatedReports:
                        type: integer
                        description: 更新的报告数
                        example: 3
              example:
                code: 0
                msg: "string"
                data:
                  refreshTime: "2024-03-12T12:00:00"
                  totalReports: 10
                  updatedReports: 3
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /reports/statistics:
    get:
      tags:
        - 报告管理
      summary: 获取报告统计信息
      description: 获取报告管理的统计数据
      operationId: getReportStatistics
      parameters:
        - name: projectId
          in: query
          description: 项目ID筛选
          required: false
          schema:
            type: integer
            format: int64
            example: 1
        - name: timeRange
          in: query
          description: 时间范围
          required: false
          schema:
            type: string
            enum: ["7天", "30天", "90天", "全部"]
            default: "30天"
            example: "30天"
      responses:
        '200':
          description: 成功获取统计信息
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      totalReports:
                        type: integer
                        description: 总报告数
                        example: 10
                      totalDefects:
                        type: integer
                        description: 总缺陷数
                        example: 1340
                      averageDefectsPerReport:
                        type: number
                        format: float
                        description: 平均每报告缺陷数
                        example: 134.0
                      riskDistribution:
                        type: object
                        description: 风险分布统计
                        properties:
                          highRisk:
                            type: integer
                            description: 高危缺陷总数
                            example: 1000
                          mediumRisk:
                            type: integer
                            description: 中危缺陷总数
                            example: 300
                          lowRisk:
                            type: integer
                            description: 低危缺陷总数
                            example: 40
                      verificationStatistics:
                        type: object
                        description: 验证统计
                        properties:
                          totalVerified:
                            type: integer
                            description: 总验证成功数
                            example: 1300
                          totalFailed:
                            type: integer
                            description: 总测试失败数
                            example: 40
                          verificationRate:
                            type: number
                            format: float
                            description: 验证成功率
                            example: 97.01
              example:
                code: 0
                msg: "string"
                data:
                  totalReports: 10
                  totalDefects: 1340
                  averageDefectsPerReport: 134.0
                  riskDistribution:
                    highRisk: 1000
                    mediumRisk: 300
                    lowRisk: 40
                  verificationStatistics:
                    totalVerified: 1300
                    totalFailed: 40
                    verificationRate: 97.01
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    ReportInfo:
      type: object
      description: 报告信息
      properties:
        id:
          type: integer
          format: int64
          description: 报告ID
          example: 1
        sequenceNumber:
          type: string
          description: 序号
          example: "01"
        reportName:
          type: string
          description: 报告名称
          example: "漏洞测试分析报告"
        projectName:
          type: string
          description: 所属项目
          example: "漏洞检测测试"
        projectId:
          type: integer
          format: int64
          description: 项目ID
          example: 1
        taskId:
          type: integer
          format: int64
          description: 任务ID
          example: 123456
        defectCount:
          type: integer
          description: 缺陷数量
          example: 134
        riskClassification:
          type: object
          description: 危险分类
          properties:
            highRisk:
              type: integer
              description: 高危数量
              example: 100
            mediumRisk:
              type: integer
              description: 中危数量
              example: 30
            lowRisk:
              type: integer
              description: 低危数量
              example: 4
        verificationSuccess:
          type: integer
          description: 验证成功数量
          example: 130
        testFailure:
          type: integer
          description: 测试失败数量
          example: 4
        reportGenerationTime:
          type: string
          description: 报告生成时间
          example: "2024-03-12 12:00:00"

    ReportDetail:
      type: object
      description: 报告详细信息
      allOf:
        - $ref: '#/components/schemas/ReportInfo'
        - type: object
          properties:
            reportContent:
              type: object
              description: 报告内容
              properties:
                summary:
                  type: object
                  description: 报告摘要
                  properties:
                    projectOverview:
                      type: string
                      description: 项目概述
                      example: "本次对漏洞检测测试项目进行了全面的安全扫描..."
                    scanScope:
                      type: string
                      description: 扫描范围
                      example: "包含Java、JavaScript等代码文件"
                    scanDuration:
                      type: string
                      description: 扫描时长
                      example: "2小时30分钟"
                defectDetails:
                  type: array
                  description: 缺陷详情列表
                  items:
                    type: object
                    properties:
                      defectId:
                        type: integer
                        format: int64
                        description: 缺陷ID
                        example: 1
                      vulnerabilityType:
                        type: string
                        description: 漏洞类型
                        example: "SQL注入"
                      defectPath:
                        type: string
                        description: 缺陷路径
                        example: "src/main/java/com/example/UserController.java"
                      defectLine:
                        type: integer
                        description: 缺陷行号
                        example: 45
                      riskLevel:
                        type: string
                        description: 风险等级
                        enum: ["高危", "中危", "低危"]
                        example: "高危"
                      description:
                        type: string
                        description: 缺陷描述
                        example: "用户输入未经过滤直接拼接到SQL语句中"
                      suggestion:
                        type: string
                        description: 修复建议
                        example: "使用参数化查询或预编译语句"
                charts:
                  type: object
                  description: 图表数据
                  properties:
                    riskDistributionChart:
                      type: object
                      description: 风险分布图表
                      example: {}
                    trendChart:
                      type: object
                      description: 趋势图表
                      example: {}
            createdName:
              type: string
              description: 创建人
              example: "admin"
            createdTime:
              type: string
              description: 创建时间
              example: "2024-03-12 10:00:00"

    ErrorResponse:
      type: object
      description: 错误响应
      required:
        - code
        - msg
      properties:
        code:
          type: integer
          description: 错误码
          example: 500
        msg:
          type: string
          description: 错误消息
          example: "操作失败"
        data:
          type: object
          nullable: true
          description: 错误详情
          example: null

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: 报告管理
    description: SAST报告管理相关接口
