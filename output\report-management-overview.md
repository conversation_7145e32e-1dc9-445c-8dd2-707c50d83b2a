# 报告管理系统接口文档

## 概述

报告管理系统提供完整的SAST分析报告管理功能，包括报告生成、列表查询、详情查看、下载导出、数据刷新等核心功能。

## 原型图功能分析

### 页面布局
- **标题**: 报告管理
- **搜索框**: 请输入项目名称
- **操作按钮**: 
  - 生成报告 (蓝色按钮)
  - 数据刷新 (刷新图标)

### 列表字段
| 字段 | 示例值 | 说明 |
|------|--------|------|
| 序号 | 01, 02, 03... | 自动生成的序列号 |
| 报告名称 | 漏洞测试分析报告 | 报告的显示名称 |
| 所属项目 | 漏洞检测测试 | 关联的项目名称 |
| 缺陷数量 | 134 | 报告中包含的缺陷总数 |
| 危险分类 | 高危100、中危30、低危4 | 按风险等级分类统计 |
| 验证成功 | 130 | 验证成功的缺陷数量 |
| 测试失败 | 4 | 测试失败的缺陷数量 |
| 报告生成时间 | 2024-03-12 12:00:00 | 报告创建时间 |
| 操作 | 查看、下载、删除 | 操作按钮 |

## 核心接口

### 1. 报告列表查询
**GET** `/api/reports`

分页查询报告信息列表，支持搜索和筛选。

**查询参数**:
- `current`: 当前页码 (默认1)
- `size`: 每页记录数 (默认10)
- `keyword`: 搜索关键词（项目名称）
- `projectId`: 项目ID筛选
- `taskId`: 任务ID筛选

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "records": [
      {
        "id": 1,
        "sequenceNumber": "01",
        "reportName": "漏洞测试分析报告",
        "projectName": "漏洞检测测试",
        "projectId": 1,
        "taskId": 123456,
        "defectCount": 134,
        "riskClassification": {
          "highRisk": 100,
          "mediumRisk": 30,
          "lowRisk": 4
        },
        "verificationSuccess": 130,
        "testFailure": 4,
        "reportGenerationTime": "2024-03-12 12:00:00"
      }
    ],
    "total": 10,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 生成报告
**POST** `/api/reports`

根据指定项目和任务生成新的分析报告。

**请求参数**:
```json
{
  "reportName": "漏洞测试分析报告",
  "projectId": 1,
  "taskId": 123456,
  "reportTemplate": "标准模板",
  "includeCharts": true
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "reportId": 11,
    "reportName": "漏洞测试分析报告",
    "generationTime": "2024-03-12 12:00:00",
    "status": "生成成功"
  }
}
```

### 3. 查看报告详情
**GET** `/api/reports/{reportId}`

获取指定报告的详细信息和内容。

### 4. 下载报告
**GET** `/api/reports/{reportId}/download`

下载指定报告的文件，支持PDF、HTML、DOCX格式。

**查询参数**:
- `format`: 下载格式 (pdf/html/docx，默认pdf)

### 5. 数据刷新
**POST** `/api/reports/refresh`

刷新报告列表数据，重新计算统计信息。

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "refreshTime": "2024-03-12T12:00:00",
    "totalReports": 10,
    "updatedReports": 3
  }
}
```

### 6. 报告统计信息
**GET** `/api/reports/statistics`

获取报告管理的统计数据。

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "totalReports": 10,
    "totalDefects": 1340,
    "averageDefectsPerReport": 134.0,
    "riskDistribution": {
      "highRisk": 1000,
      "mediumRisk": 300,
      "lowRisk": 40
    },
    "verificationStatistics": {
      "totalVerified": 1300,
      "totalFailed": 40,
      "verificationRate": 97.01
    }
  }
}
```

## 数据模型

### 数据库字段映射

| 原型图字段 | 数据库字段 | 类型 | 说明 |
|-----------|-----------|------|------|
| 序号 | - | - | 前端生成的序列号 |
| 报告名称 | report_name | varchar(255) | 报告显示名称 |
| 所属项目 | project_name | varchar(255) | 通过project_id关联获取 |
| 缺陷数量 | - | - | 统计计算得出 |
| 危险分类 | - | - | 统计计算得出 |
| 验证成功 | - | - | 统计计算得出 |
| 测试失败 | - | - | 统计计算得出 |
| 报告生成时间 | report_generation_time | timestamp(6) | 报告创建时间 |

### 统计数据计算逻辑

#### 1. 缺陷数量统计
```sql
-- 计算报告对应任务的缺陷总数
SELECT COUNT(*) as defectCount
FROM app_sast_defect_info d
WHERE d.task_id = ? AND d.del_flag = 0;
```

#### 2. 危险分类统计
```sql
-- 按风险等级分组统计
SELECT 
    defect_level,
    COUNT(*) as count
FROM app_sast_defect_info d
WHERE d.task_id = ? AND d.del_flag = 0
GROUP BY defect_level;
```

#### 3. 验证统计
```sql
-- 验证成功和失败统计
SELECT 
    SUM(CASE WHEN defect_verification_result = '验证成功' THEN 1 ELSE 0 END) as verificationSuccess,
    SUM(CASE WHEN defect_verification_result = '验证失败' THEN 1 ELSE 0 END) as testFailure
FROM app_sast_defect_info d
WHERE d.task_id = ? AND d.del_flag = 0;
```

## 业务规则

### 1. 报告生成规则
- 只有完成扫描的任务才能生成报告
- 报告名称在同一项目下不能重复
- 生成报告时自动计算统计数据
- 支持多种报告模板选择

### 2. 数据统计规则
- 统计数据基于任务关联的缺陷信息
- 只统计未删除的缺陷记录 (del_flag=0)
- 验证成功率 = 验证成功数 / (验证成功数 + 验证失败数) * 100%
- 数据刷新时重新计算所有统计信息

### 3. 权限控制规则
- 项目成员可查看所属项目的报告
- 管理员可查看所有项目的报告
- 报告生成需要项目管理员权限
- 报告删除需要管理员权限

### 4. 文件管理规则
- 报告文件存储在指定目录
- 支持多种格式导出 (PDF/HTML/DOCX)
- 文件下载链接有时效性限制
- 删除报告时同时删除关联文件

## 前端实现建议

### 1. 列表展示组件
```javascript
// 危险分类显示组件
const RiskClassification = ({ riskData }) => (
  <Space>
    <Tag color="red">高危{riskData.highRisk}</Tag>
    <Tag color="orange">中危{riskData.mediumRisk}</Tag>
    <Tag color="yellow">低危{riskData.lowRisk}</Tag>
  </Space>
);

// 操作列组件
const ActionColumn = ({ record }) => (
  <Space>
    <Button 
      type="link" 
      icon={<EyeOutlined />}
      onClick={() => handleView(record.id)}
    >
      查看
    </Button>
    <Button 
      type="link" 
      icon={<DownloadOutlined />}
      onClick={() => handleDownload(record.id)}
    >
      下载
    </Button>
    <Popconfirm 
      title="确定删除此报告吗？"
      onConfirm={() => handleDelete(record.id)}
    >
      <Button 
        type="link" 
        danger
        icon={<DeleteOutlined />}
      >
        删除
      </Button>
    </Popconfirm>
  </Space>
);
```

### 2. 生成报告弹窗
```javascript
const GenerateReportModal = ({ visible, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [generating, setGenerating] = useState(false);

  const handleGenerate = async (values) => {
    setGenerating(true);
    try {
      const result = await generateReport(values);
      message.success('报告生成成功');
      onSuccess(result.data);
      onCancel();
    } catch (error) {
      message.error('报告生成失败');
    } finally {
      setGenerating(false);
    }
  };

  return (
    <Modal 
      title="生成报告" 
      visible={visible} 
      onCancel={onCancel}
      footer={null}
    >
      <Form form={form} onFinish={handleGenerate}>
        <Form.Item name="reportName" label="报告名称" rules={[{ required: true }]}>
          <Input placeholder="请输入报告名称" />
        </Form.Item>
        
        <Form.Item name="projectId" label="选择项目" rules={[{ required: true }]}>
          <Select placeholder="请选择项目">
            {/* 项目选项 */}
          </Select>
        </Form.Item>
        
        <Form.Item name="taskId" label="选择任务" rules={[{ required: true }]}>
          <Select placeholder="请选择任务">
            {/* 任务选项 */}
          </Select>
        </Form.Item>
        
        <Form.Item name="reportTemplate" label="报告模板">
          <Select defaultValue="标准模板">
            <Option value="标准模板">标准模板</Option>
            <Option value="详细模板">详细模板</Option>
            <Option value="简化模板">简化模板</Option>
          </Select>
        </Form.Item>
        
        <Form.Item name="includeCharts" valuePropName="checked">
          <Checkbox>包含图表</Checkbox>
        </Form.Item>
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={generating}>
              生成报告
            </Button>
            <Button onClick={onCancel}>取消</Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};
```

### 3. 数据刷新功能
```javascript
const ReportManagement = () => {
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshReportData();
      message.success('数据刷新成功');
      // 重新加载列表数据
      await loadReportList();
    } catch (error) {
      message.error('数据刷新失败');
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button type="primary" onClick={() => setGenerateVisible(true)}>
            生成报告
          </Button>
          <Button 
            icon={<ReloadOutlined />} 
            loading={refreshing}
            onClick={handleRefresh}
          >
            数据刷新
          </Button>
        </Space>
      </div>
      
      {/* 报告列表表格 */}
      <Table 
        columns={columns}
        dataSource={reportList}
        loading={loading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.size,
          total: pagination.total,
          onChange: handlePageChange
        }}
      />
    </div>
  );
};
```

## 性能优化

### 1. 数据库优化
- 在project_id、task_id字段上建立索引
- 报告统计数据使用缓存机制
- 大数据量分页使用游标分页

### 2. 文件处理优化
- 报告生成使用异步处理
- 文件下载使用CDN加速
- 大文件支持断点续传

### 3. 前端优化
- 列表使用虚拟滚动处理大数据量
- 报告预览使用懒加载
- 文件下载显示进度条

## 安全考虑

### 1. 权限控制
- 基于项目权限控制报告访问
- 文件下载链接包含访问令牌
- 敏感信息在报告中脱敏处理

### 2. 文件安全
- 上传文件类型和大小限制
- 文件存储路径随机化
- 定期清理过期文件

## 扩展功能

### 1. 报告模板管理
- 支持自定义报告模板
- 模板版本控制
- 模板预览功能

### 2. 报告分享
- 生成分享链接
- 设置访问权限和有效期
- 分享记录追踪

### 3. 报告对比
- 支持多个报告对比
- 趋势分析图表
- 改进建议生成

## 总结

报告管理系统提供了完整的SAST分析报告管理功能，支持：

1. **完整的报告生命周期管理**: 生成、查看、下载、删除
2. **丰富的统计分析**: 缺陷统计、风险分析、验证统计
3. **灵活的搜索筛选**: 支持多维度搜索和筛选
4. **多格式导出**: 支持PDF、HTML、DOCX格式
5. **实时数据刷新**: 保证数据的准确性和时效性

接口设计遵循RESTful规范，响应格式统一，便于前端开发和后续维护。
