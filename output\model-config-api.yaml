openapi: 3.0.3
info:
  title: 模型配置管理API
  description: SAST大模型配置管理相关接口文档
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.example.com
    description: 生产环境

paths:
  /models:
    get:
      tags:
        - 模型配置
      summary: 获取模型配置列表（分页）
      description: 分页查询模型配置信息列表，支持搜索和筛选
      operationId: getModelList
      parameters:
        - name: current
          in: query
          description: 当前页码
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: size
          in: query
          description: 每页记录数
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
        - name: keyword
          in: query
          description: 搜索关键词（模型名称）
          required: false
          schema:
            type: string
            example: "GPT-4"
        - name: supplier
          in: query
          description: 供应商筛选
          required: false
          schema:
            type: string
            example: "OpenAI"
        - name: testStatus
          in: query
          description: 测试状态筛选
          required: false
          schema:
            type: string
            enum: ["正常", "异常", "未测试"]
            example: "正常"
        - name: enabledOnly
          in: query
          description: 仅显示启用的模型
          required: false
          schema:
            type: boolean
            default: false
            example: true
      responses:
        '200':
          description: 成功获取模型配置列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      records:
                        type: array
                        items:
                          $ref: '#/components/schemas/ModelConfig'
                      total:
                        type: integer
                        description: 总记录数
                        example: 3
                      size:
                        type: integer
                        description: 每页记录数
                        example: 10
                      current:
                        type: integer
                        description: 当前页码
                        example: 1
                      pages:
                        type: integer
                        description: 总页数
                        example: 1
              example:
                code: 0
                msg: "string"
                data:
                  records:
                    - id: 1
                      sequenceNumber: "01"
                      modelName: "GPT-4 通用模型"
                      supplier: "OpenAI"
                      modelIdentifier: "gpt-4-turbo"
                      weightSetting: 30
                      currentStatus: "已启用"
                      testStatus: "正常"
                      modifyTime: "2024-03-12 12:00:00"
                    - id: 2
                      sequenceNumber: "02"
                      modelName: "文心一言安全版"
                      supplier: "百度"
                      modelIdentifier: "ERNIE-Bot-Security"
                      weightSetting: 20
                      currentStatus: "已禁用"
                      testStatus: "正常"
                      modifyTime: "2024-03-12 12:00:00"
                    - id: 3
                      sequenceNumber: "03"
                      modelName: "Claude 3"
                      supplier: "Anthropic"
                      modelIdentifier: "claude-3-opus"
                      weightSetting: 50
                      currentStatus: "已启用"
                      testStatus: "正常"
                      modifyTime: "2024-03-12 12:00:00"
                  total: 3
                  size: 10
                  current: 1
                  pages: 1
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      tags:
        - 模型配置
      summary: 新增模型配置
      description: 创建新的模型配置
      operationId: createModel
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateModelRequest'
            example:
              modelName: "GPT-4 通用模型"
              supplier: "OpenAI"
              modelIdentifier: "gpt-4-turbo"
              apiType: "REST"
              baseUrl: "https://api.openai.com/v1"
              apiKey: "sk-xxxxxxxxxxxxxxxx"
              maxTokens: 4000
              temperature: 0.7
              timeoutSetting: 30
              weightSetting: 0.3
              extraHeaders: {}
      responses:
        '200':
          description: 模型配置创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      modelId:
                        type: integer
                        format: int64
                        description: 创建的模型ID
                        example: 4
              example:
                code: 0
                msg: "string"
                data:
                  modelId: 4
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /models/{modelId}:
    get:
      tags:
        - 模型配置
      summary: 获取模型配置详情
      description: 获取指定模型的详细配置信息
      operationId: getModelDetail
      parameters:
        - name: modelId
          in: path
          description: 模型ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      responses:
        '200':
          description: 成功获取模型配置详情
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    $ref: '#/components/schemas/ModelConfigDetail'
        '404':
          description: 模型配置不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - 模型配置
      summary: 更新模型配置
      description: 更新指定模型的配置信息
      operationId: updateModel
      parameters:
        - name: modelId
          in: path
          description: 模型ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateModelRequest'
      responses:
        '200':
          description: 模型配置更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    nullable: true
                    example: null
        '404':
          description: 模型配置不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      tags:
        - 模型配置
      summary: 删除模型配置
      description: 删除指定的模型配置（软删除）
      operationId: deleteModel
      parameters:
        - name: modelId
          in: path
          description: 模型ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      responses:
        '200':
          description: 模型配置删除成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    nullable: true
                    example: null
        '404':
          description: 模型配置不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /models/{modelId}/test:
    post:
      tags:
        - 模型配置
      summary: 测试模型连接
      description: 测试指定模型的API连接是否正常
      operationId: testModel
      parameters:
        - name: modelId
          in: path
          description: 模型ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      responses:
        '200':
          description: 模型测试完成
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      testResult:
                        type: string
                        description: 测试结果
                        enum: ["正常", "异常"]
                        example: "正常"
                      testMessage:
                        type: string
                        description: 测试消息
                        example: "模型连接正常，响应时间: 1.2秒"
                      responseTime:
                        type: number
                        format: float
                        description: 响应时间（秒）
                        example: 1.2
                      testTime:
                        type: string
                        format: date-time
                        description: 测试时间
                        example: "2024-03-12T12:00:00"
              example:
                code: 0
                msg: "string"
                data:
                  testResult: "正常"
                  testMessage: "模型连接正常，响应时间: 1.2秒"
                  responseTime: 1.2
                  testTime: "2024-03-12T12:00:00"
        '404':
          description: 模型配置不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /models/{modelId}/enable:
    post:
      tags:
        - 模型配置
      summary: 启用模型
      description: 启用指定的模型配置
      operationId: enableModel
      parameters:
        - name: modelId
          in: path
          description: 模型ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      responses:
        '200':
          description: 模型启用成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    nullable: true
                    example: null
        '404':
          description: 模型配置不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /models/{modelId}/disable:
    post:
      tags:
        - 模型配置
      summary: 禁用模型
      description: 禁用指定的模型配置
      operationId: disableModel
      parameters:
        - name: modelId
          in: path
          description: 模型ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      responses:
        '200':
          description: 模型禁用成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    nullable: true
                    example: null
        '404':
          description: 模型配置不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    ModelConfig:
      type: object
      description: 模型配置信息
      properties:
        id:
          type: integer
          format: int64
          description: 模型ID
          example: 1
        sequenceNumber:
          type: string
          description: 序号
          example: "01"
        modelName:
          type: string
          description: 模型名称
          example: "GPT-4 通用模型"
        supplier:
          type: string
          description: 组织商/供应商
          example: "OpenAI"
        modelIdentifier:
          type: string
          description: 模型标识
          example: "gpt-4-turbo"
        weightSetting:
          type: number
          format: float
          description: 收费设置/权重设置（百分比）
          minimum: 0
          maximum: 100
          example: 30
        currentStatus:
          type: string
          description: 模型当前状态
          enum: ["已启用", "已禁用"]
          example: "已启用"
        testStatus:
          type: string
          description: 测试状态
          enum: ["正常", "异常", "未测试"]
          example: "正常"
        modifyTime:
          type: string
          description: 更新时间
          example: "2024-03-12 12:00:00"

    ModelConfigDetail:
      type: object
      description: 模型配置详细信息
      allOf:
        - $ref: '#/components/schemas/ModelConfig'
        - type: object
          properties:
            apiType:
              type: string
              description: API类型
              example: "REST"
            baseUrl:
              type: string
              description: 基础URL
              example: "https://api.openai.com/v1"
            apiKey:
              type: string
              description: API密钥（脱敏显示）
              example: "sk-**********************"
            maxTokens:
              type: integer
              description: 最大token数
              example: 4000
            temperature:
              type: number
              format: float
              description: 输出温度
              minimum: 0
              maximum: 1
              example: 0.7
            timeoutSetting:
              type: integer
              description: 超时设置（秒）
              example: 30
            extraHeaders:
              type: object
              description: 额外的请求头
              example: {}
            createdName:
              type: string
              description: 创建人
              example: "admin"
            createdTime:
              type: string
              description: 创建时间
              example: "2024-03-12 10:00:00"

    CreateModelRequest:
      type: object
      description: 创建模型配置请求
      required:
        - modelName
        - supplier
        - modelIdentifier
        - apiType
        - baseUrl
        - apiKey
      properties:
        modelName:
          type: string
          description: 模型名称
          maxLength: 255
          example: "GPT-4 通用模型"
        supplier:
          type: string
          description: 供应商
          maxLength: 255
          example: "OpenAI"
        modelIdentifier:
          type: string
          description: 模型标识
          maxLength: 255
          example: "gpt-4-turbo"
        apiType:
          type: string
          description: API类型
          maxLength: 100
          example: "REST"
        baseUrl:
          type: string
          description: 基础URL
          maxLength: 500
          example: "https://api.openai.com/v1"
        apiKey:
          type: string
          description: API密钥
          maxLength: 500
          example: "sk-xxxxxxxxxxxxxxxx"
        maxTokens:
          type: integer
          description: 最大token数
          default: 4000
          minimum: 1
          maximum: 100000
          example: 4000
        temperature:
          type: number
          format: float
          description: 输出温度
          default: 0.7
          minimum: 0
          maximum: 1
          example: 0.7
        timeoutSetting:
          type: integer
          description: 超时设置（秒）
          default: 30
          minimum: 1
          maximum: 300
          example: 30
        weightSetting:
          type: number
          format: float
          description: 权重设置
          default: 0.5
          minimum: 0
          maximum: 1
          example: 0.3
        extraHeaders:
          type: object
          description: 额外的请求头
          example: {}

    UpdateModelRequest:
      type: object
      description: 更新模型配置请求
      properties:
        modelName:
          type: string
          description: 模型名称
          maxLength: 255
          example: "GPT-4 通用模型"
        supplier:
          type: string
          description: 供应商
          maxLength: 255
          example: "OpenAI"
        modelIdentifier:
          type: string
          description: 模型标识
          maxLength: 255
          example: "gpt-4-turbo"
        apiType:
          type: string
          description: API类型
          maxLength: 100
          example: "REST"
        baseUrl:
          type: string
          description: 基础URL
          maxLength: 500
          example: "https://api.openai.com/v1"
        apiKey:
          type: string
          description: API密钥
          maxLength: 500
          example: "sk-xxxxxxxxxxxxxxxx"
        maxTokens:
          type: integer
          description: 最大token数
          minimum: 1
          maximum: 100000
          example: 4000
        temperature:
          type: number
          format: float
          description: 输出温度
          minimum: 0
          maximum: 1
          example: 0.7
        timeoutSetting:
          type: integer
          description: 超时设置（秒）
          minimum: 1
          maximum: 300
          example: 30
        weightSetting:
          type: number
          format: float
          description: 权重设置
          minimum: 0
          maximum: 1
          example: 0.3
        extraHeaders:
          type: object
          description: 额外的请求头
          example: {}

    ErrorResponse:
      type: object
      description: 错误响应
      required:
        - code
        - msg
      properties:
        code:
          type: integer
          description: 错误码
          example: 500
        msg:
          type: string
          description: 错误消息
          example: "操作失败"
        data:
          type: object
          nullable: true
          description: 错误详情
          example: null

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: 模型配置
    description: 大模型配置管理相关接口
