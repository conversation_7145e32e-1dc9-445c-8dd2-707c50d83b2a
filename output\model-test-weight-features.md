# 模型测试和权重配置功能接口文档

## 概述

根据原型图分析，这两个功能是模型配置管理的重要组成部分：
1. **模型测试弹窗** - 用于测试模型连接和功能验证
2. **权重配置弹窗** - 用于配置模型权重分配

## 功能一：模型测试

### 原型图分析
- **标题**: 模型测试
- **选择模型**: 下拉选择框，显示"请选择"
- **测试输入**: 大文本框，提示"请输入测试内容，例如一段代码，用户输入或系统目标，用于模型检测..."
- **开始测试**: 蓝色按钮
- **模型输出**: 显示区域，提示"模型输出将显示在这里"
- **统计信息**: 响应时间: N/A, Token消耗: N/A
- **操作按钮**: 保存、取消

### 核心接口

#### 1. 获取可用模型列表
**GET** `/api/models/available`

获取所有可用于测试的模型列表，用于下拉选择框。

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "modelId": 1,
      "modelName": "GPT-4 通用模型",
      "supplier": "OpenAI",
      "modelIdentifier": "gpt-4-turbo",
      "status": "正常"
    },
    {
      "modelId": 2,
      "modelName": "文心一言安全版",
      "supplier": "百度",
      "modelIdentifier": "ERNIE-Bot-Security",
      "status": "未测试"
    }
  ]
}
```

#### 2. 执行模型测试
**POST** `/api/models/test`

对指定模型进行功能测试。

**请求参数**:
```json
{
  "modelId": 1,
  "testContent": "请输入测试内容，例如一段代码，用户输入或系统目标，用于模型检测..."
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "testId": "test_20240312_001",
    "modelId": 1,
    "modelName": "GPT-4 通用模型",
    "testContent": "请输入测试内容...",
    "testOutput": "模型输出将显示在这里",
    "responseTime": 2.5,
    "tokenUsage": 150,
    "testStatus": "成功",
    "testTime": "2024-03-12T12:00:00",
    "errorMessage": null
  }
}
```

### 前端实现建议

```javascript
// 模型测试组件
const ModelTestModal = () => {
  const [form] = Form.useForm();
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [models, setModels] = useState([]);

  // 获取可用模型列表
  useEffect(() => {
    fetchAvailableModels().then(setModels);
  }, []);

  // 执行测试
  const handleTest = async (values) => {
    setTesting(true);
    try {
      const result = await executeModelTest(values);
      setTestResult(result.data);
    } catch (error) {
      message.error('测试失败');
    } finally {
      setTesting(false);
    }
  };

  return (
    <Modal title="模型测试" visible={visible} onCancel={onCancel}>
      <Form form={form} onFinish={handleTest}>
        <Form.Item name="modelId" label="选择模型" rules={[{ required: true }]}>
          <Select placeholder="请选择">
            {models.map(model => (
              <Option key={model.modelId} value={model.modelId}>
                {model.modelName}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item name="testContent" label="测试输入" rules={[{ required: true }]}>
          <TextArea 
            rows={6}
            placeholder="请输入测试内容，例如一段代码，用户输入或系统目标，用于模型检测..."
          />
        </Form.Item>
        
        <Button 
          type="primary" 
          htmlType="submit" 
          loading={testing}
          block
        >
          开始测试
        </Button>
      </Form>
      
      {testResult && (
        <div style={{ marginTop: 16 }}>
          <div>
            <strong>模型输出:</strong>
            <div style={{ 
              border: '1px solid #d9d9d9', 
              padding: 8, 
              marginTop: 8,
              minHeight: 100 
            }}>
              {testResult.testOutput || '模型输出将显示在这里'}
            </div>
          </div>
          
          <div style={{ marginTop: 16, display: 'flex', justifyContent: 'space-between' }}>
            <span>响应时间: {testResult.responseTime || 'N/A'}秒</span>
            <span>Token消耗: {testResult.tokenUsage || 'N/A'}</span>
          </div>
        </div>
      )}
    </Modal>
  );
};
```

## 功能二：权重配置

### 原型图分析
- **标题**: 权重配置
- **提示信息**: "为各个模型配置权重，总和必须为100%"
- **平均分配**: 绿色按钮
- **模型列表**:
  - GPT-4 漏洞检测: 30%
  - 文心一言安全版: 20%
  - Claude-3: 50%
- **当前总和**: 100% (蓝色显示)
- **操作按钮**: 保存、取消

### 核心接口

#### 1. 获取模型权重配置
**GET** `/api/models/weights`

获取当前所有模型的权重配置信息。

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "models": [
      {
        "modelId": 1,
        "modelName": "GPT-4 漏洞检测",
        "currentWeight": 30,
        "maxWeight": 100,
        "minWeight": 0,
        "status": "已启用"
      },
      {
        "modelId": 2,
        "modelName": "文心一言安全版",
        "currentWeight": 20,
        "maxWeight": 100,
        "minWeight": 0,
        "status": "已启用"
      },
      {
        "modelId": 3,
        "modelName": "Claude-3",
        "currentWeight": 50,
        "maxWeight": 100,
        "minWeight": 0,
        "status": "已启用"
      }
    ],
    "totalWeight": 100,
    "isValid": true
  }
}
```

#### 2. 更新模型权重配置
**PUT** `/api/models/weights`

批量更新模型权重配置。

**请求参数**:
```json
{
  "weights": [
    { "modelId": 1, "weight": 30 },
    { "modelId": 2, "weight": 20 },
    { "modelId": 3, "weight": 50 }
  ],
  "autoBalance": false
}
```

#### 3. 自动平均分配权重
**POST** `/api/models/weights/auto-balance`

对所有启用的模型进行平均权重分配。

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "balancedModels": [
      {
        "modelId": 1,
        "modelName": "GPT-4 漏洞检测",
        "currentWeight": 33.33,
        "status": "已启用"
      }
    ],
    "averageWeight": 33.33
  }
}
```

### 前端实现建议

```javascript
// 权重配置组件
const WeightConfigModal = () => {
  const [weights, setWeights] = useState([]);
  const [totalWeight, setTotalWeight] = useState(0);

  // 获取权重配置
  useEffect(() => {
    fetchModelWeights().then(data => {
      setWeights(data.models);
      setTotalWeight(data.totalWeight);
    });
  }, []);

  // 更新权重
  const handleWeightChange = (modelId, value) => {
    const newWeights = weights.map(item => 
      item.modelId === modelId 
        ? { ...item, currentWeight: value }
        : item
    );
    setWeights(newWeights);
    
    const total = newWeights.reduce((sum, item) => sum + item.currentWeight, 0);
    setTotalWeight(total);
  };

  // 平均分配
  const handleAutoBalance = async () => {
    try {
      const result = await autoBalanceWeights();
      setWeights(result.data.balancedModels);
      setTotalWeight(100);
      message.success('权重已平均分配');
    } catch (error) {
      message.error('分配失败');
    }
  };

  // 保存权重配置
  const handleSave = async () => {
    if (totalWeight !== 100) {
      message.error('权重总和必须为100%');
      return;
    }
    
    try {
      await updateModelWeights({
        weights: weights.map(item => ({
          modelId: item.modelId,
          weight: item.currentWeight
        }))
      });
      message.success('权重配置已保存');
      onCancel();
    } catch (error) {
      message.error('保存失败');
    }
  };

  return (
    <Modal 
      title="权重配置" 
      visible={visible} 
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>取消</Button>,
        <Button key="save" type="primary" onClick={handleSave}>保存</Button>
      ]}
    >
      <div style={{ marginBottom: 16 }}>
        <Alert 
          message="为各个模型配置权重，总和必须为100%" 
          type="info" 
          showIcon 
        />
        <Button 
          type="default" 
          size="small" 
          style={{ marginTop: 8 }}
          onClick={handleAutoBalance}
        >
          平均分配
        </Button>
      </div>
      
      {weights.map(model => (
        <div key={model.modelId} style={{ 
          display: 'flex', 
          alignItems: 'center', 
          marginBottom: 12 
        }}>
          <span style={{ width: 120 }}>{model.modelName}</span>
          <InputNumber
            min={0}
            max={100}
            value={model.currentWeight}
            onChange={(value) => handleWeightChange(model.modelId, value)}
            formatter={value => `${value}%`}
            parser={value => value.replace('%', '')}
            style={{ width: 100 }}
          />
        </div>
      ))}
      
      <div style={{ marginTop: 16, textAlign: 'center' }}>
        <span>当前总和: </span>
        <span style={{ 
          color: totalWeight === 100 ? '#1890ff' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {totalWeight}%
        </span>
      </div>
    </Modal>
  );
};
```

## 数据库设计

### 模型测试记录表
```sql
CREATE TABLE app_sast_model_test_log (
    id BIGINT PRIMARY KEY DEFAULT snow_next_id_bigint(),
    test_id VARCHAR(50) NOT NULL,
    model_id BIGINT NOT NULL,
    test_content TEXT NOT NULL,
    test_output TEXT,
    response_time DECIMAL(10,3),
    token_usage INTEGER,
    test_status VARCHAR(20) DEFAULT '进行中',
    error_message TEXT,
    created_time TIMESTAMP DEFAULT NOW(),
    created_id BIGINT NOT NULL,
    created_name VARCHAR(50) NOT NULL
);
```

### 权重配置更新
```sql
-- 在现有模型配置表中使用 weight_setting 字段
-- 权重值范围: 0.00-1.00，前端显示为百分比
UPDATE app_sast_model_config 
SET weight_setting = 0.30 
WHERE id = 1;
```

## 业务规则

### 模型测试规则
1. 只有状态为"正常"或"未测试"的模型可以进行测试
2. 测试内容不能为空，最大长度10000字符
3. 测试超时时间为60秒
4. 测试结果保存到日志表，便于历史查询

### 权重配置规则
1. 所有启用模型的权重总和必须为100%
2. 单个模型权重范围: 0%-100%
3. 禁用的模型权重自动设为0%
4. 权重变更需要管理员权限

## 错误处理

### 常见错误场景
- 模型不存在或已禁用 (400)
- 测试内容为空 (400)
- 权重总和不为100% (400)
- 模型连接超时 (500)
- 权限不足 (403)

### 错误响应示例
```json
{
  "code": 400,
  "msg": "权重总和必须为100%",
  "data": {
    "currentTotal": 95,
    "expectedTotal": 100
  }
}
```

## 总结

这两个功能为模型配置管理提供了重要的测试和配置能力：

### 模型测试功能
- **实时测试**: 支持实时测试模型连接和功能
- **详细反馈**: 提供响应时间、Token消耗等详细信息
- **历史记录**: 保存测试历史，便于问题排查

### 权重配置功能
- **灵活配置**: 支持自定义权重分配
- **自动平衡**: 一键平均分配功能
- **实时验证**: 实时检查权重总和是否有效

这些接口设计完全满足原型图的功能需求，提供了良好的用户体验和系统可靠性。
