# 项目管理-任务列表接口文档

## 接口概述

**接口名称**: 获取项目任务列表  
**接口路径**: `/api/project-management/{projectId}/tasks`  
**请求方法**: `GET`  
**接口描述**: 获取指定项目下的任务列表，包含任务基本信息、文件上传状态、分析状态、漏洞统计等信息（未分页列表）

## 请求参数

### Path Parameters

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| projectId | integer | 是 | 项目ID | 1 |

### Query Parameters

| 参数名 | 类型 | 必填 | 默认值 | 描述 | 示例 |
|--------|------|------|--------|------|------|
| keyword | string | 否 | - | 搜索关键词（支持任务名称、创建人模糊搜索） | "测试任务" |

### 请求示例

```http
GET /api/project-management/1/tasks?keyword=测试
```

## 响应格式

### 成功响应 (200)

**响应体结构**: 使用 `R<Object>` 格式（规则二），包含统计汇总信息和未分页任务列表

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "summary": {
      "totalTasks": 265,
      "highRisk": 140,
      "highRiskPercent": 30,
      "mediumRisk": 109,
      "mediumRiskPercent": 30,
      "lowRisk": 87,
      "lowRiskPercent": 40
    },
    "taskList": [
      {
        "taskId": "TASK-20250723-001",
        "taskName": "测试智能化系统",
        "fileUploadStatus": {
          "sourceFileProgress": 100,
          "configFileProgress": 100
        },
        "analysisStatus": "AI分析完成",
        "analysisResults": {
          "highRisk": 12,
          "mediumRisk": 8,
          "lowRisk": 5
        },
        "updateTime": "2024-03-12T12:00:00"
      }
    ]
  }
}
```

### 响应字段说明

#### Data 对象

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| summary | object | 否 | 统计汇总信息 | 见TaskSummary对象 |
| taskList | array | 否 | 任务列表数据 | 见TaskListItem对象数组 |

#### TaskSummary 对象（统计汇总）

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| totalTasks | integer | 否 | 测试总数 | 265 |
| highRisk | integer | 否 | 高危漏洞总数 | 140 |
| highRiskPercent | integer | 否 | 高危漏洞占比(%) | 30 |
| mediumRisk | integer | 否 | 中危漏洞总数 | 109 |
| mediumRiskPercent | integer | 否 | 中危漏洞占比(%) | 30 |
| lowRisk | integer | 否 | 低危漏洞总数 | 87 |
| lowRiskPercent | integer | 否 | 低危漏洞占比(%) | 40 |

#### TaskListItem 对象

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| taskId | string | 是 | 任务ID | "TASK-20250723-001" |
| taskName | string | 是 | 任务名称 | "测试智能化系统" |
| fileUploadStatus | object | 否 | 文件上传状态 | 见FileUploadStatus对象 |
| analysisStatus | string | 否 | 分析状态 | "AI分析完成" |
| analysisResults | object | 否 | 分析结果 | 见AnalysisResults对象 |
| updateTime | string | 是 | 更新时间 | "2024-03-12T12:00:00" |

#### FileUploadStatus 对象

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| sourceFileProgress | integer | 否 | 源文件上传进度(%) | 100 |
| configFileProgress | integer | 否 | 环境配置文件上传进度(%) | 100 |

#### AnalysisResults 对象

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| highRisk | integer | 否 | 高危漏洞数量 | 12 |
| mediumRisk | integer | 否 | 中危漏洞数量 | 8 |
| lowRisk | integer | 否 | 低危漏洞数量 | 5 |

### 分析状态枚举值

| 状态值 | 描述 |
|--------|------|
| 排队 | 任务排队等待处理 |
| 传播器查找 | 正在进行传播器查找 |
| 链路分析 | 正在进行链路分析 |
| 研判 | 正在进行安全研判 |
| POC验证 | 正在进行POC验证 |
| 修复建议生成 | 正在生成修复建议 |
| AI分析完成 | 分析已完成 |
| 分析失败 | 分析过程失败 |

### 错误响应

#### 400 - 请求参数错误

```json
{
  "code": 400,
  "msg": "参数错误",
  "data": null
}
```

#### 500 - 服务器内部错误

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

## 业务逻辑说明

### 数据来源

1. **任务基本信息**: 来自 `app_sast_task_management` 表
2. **文件上传状态**: 来自 `app_sast_task_files` 表统计得出
3. **分析状态**: 来自 `app_sast_task_management` 表的 `task_status` 字段
4. **漏洞统计**: 来自 `app_sast_defect_info` 表按 `defect_level` 字段统计得出

### 统计汇总逻辑

- **测试总数**: 指定项目下所有未删除的任务数量
- **漏洞统计**: 统计项目下所有任务的漏洞总数，按危险等级分类
- **占比计算**: 各等级漏洞数量占总漏洞数量的百分比

### 文件上传状态逻辑

- **源文件进度**: 基于 `app_sast_task_files` 表中 `file_type` 为源文件类型的上传情况
- **配置文件进度**: 基于 `app_sast_task_files` 表中 `file_type` 为配置文件类型的上传情况
- 进度值范围: 0-100，表示上传完成的百分比

### 响应格式说明

- **遵循规则二**: 使用普通的响应格式 `R<Object>`，不包含分页信息
- **未分页列表**: 返回项目下所有任务，不进行分页处理
- **统计汇总**: 在 `data.summary` 中提供项目级别的统计信息
- **任务列表**: 在 `data.taskList` 中提供完整的任务列表

## 权限要求

- 需要用户登录认证
- 需要项目管理相关权限
- 只能查看有权限的项目下的任务

## 注意事项

1. 所有时间字段均为ISO 8601格式
2. projectId为路径参数，用于指定查询的项目
3. 搜索关键词支持任务名称、创建人的模糊搜索
4. 删除标识为1的记录不会在列表中显示
5. 漏洞统计数据实时计算，确保数据准确性
6. 文件上传进度实时更新，反映当前上传状态
7. 该接口属于项目管理模块的子功能，需要先选择项目才能查看任务
8. **重要**: 该接口返回未分页的完整任务列表，遵循响应规范规则二
