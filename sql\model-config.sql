-- auto-generated definition
create table app_sast_model_config
(
    id               bigint        default snow_next_id_bigint()       not null
        primary key,
    model_name       varchar(255)                                      not null,
    supplier         varchar(255)                                      not null,
    model_identifier varchar(255)                                      not null,
    api_type         varchar(100)                                      not null,
    base_url         varchar(500)                                      not null,
    api_key          varchar(500)                                      not null,
    max_tokens       integer       default 4000                        not null,
    temperature      numeric(3, 2) default 0.7                         not null,
    timeout_setting  integer       default 30                          not null,
    extra_headers    jsonb,
    weight_setting   numeric(3, 2) default 0.5                         not null,
    test_status      varchar(50)   default '未测试'::character varying not null,
    created_id       bigint                                            not null,
    created_name     varchar(50)                                       not null,
    created_time     timestamp     default now(),
    modify_id        bigint,
    modify_name      varchar(50),
    modify_time      timestamp     default now(),
    del_flag         smallint      default 0                           not null
);

comment on table app_sast_model_config is 'SAST大模型配置表';

comment on column app_sast_model_config.id is '主键ID';

comment on column app_sast_model_config.model_name is '模型名称，必填';

comment on column app_sast_model_config.supplier is '供应商，如OpenAI/Claude等';

comment on column app_sast_model_config.model_identifier is '模型标识，必填';

comment on column app_sast_model_config.api_type is 'API类型，如REST/WebSocket等';

comment on column app_sast_model_config.base_url is '基础URL，必填';

comment on column app_sast_model_config.api_key is 'API密钥，必填';

comment on column app_sast_model_config.max_tokens is '最大token数，默认4000';

comment on column app_sast_model_config.temperature is '输出温度(0.00-1.00)';

comment on column app_sast_model_config.timeout_setting is '超时设置(秒)，默认30';

comment on column app_sast_model_config.extra_headers is '额外的请求头，JSON格式';

comment on column app_sast_model_config.weight_setting is '权重设置(0.00-1.00)';

comment on column app_sast_model_config.test_status is '模型测试状态：正常/异常/未测试';

comment on column app_sast_model_config.created_id is '创建人ID';

comment on column app_sast_model_config.created_name is '创建人姓名';

comment on column app_sast_model_config.created_time is '创建时间';

comment on column app_sast_model_config.modify_id is '更新人ID';

comment on column app_sast_model_config.modify_name is '更新人姓名';

comment on column app_sast_model_config.modify_time is '更新时间';

comment on column app_sast_model_config.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_model_config
    owner to postgres;

create index idx_app_sast_model_config_supplier
    on app_sast_model_config (supplier);

create index idx_app_sast_model_config_test_status
    on app_sast_model_config (test_status);

