# 任务缺陷树形结构接口文档

## 概述

根据原型图分析，这是一个展示任务缺陷分类的树形结构界面。该接口根据任务ID获取缺陷信息，并按漏洞类型进行分组展示，支持搜索和筛选功能。

## 原型图功能分析

### 树形结构展示
- **根节点**: ALL(17) - 显示总缺陷数量
- **分类节点**: 按漏洞类型分组
  - SQL注入(5) - 5个SQL注入类型缺陷
  - RCE(12) - 12个远程代码执行类型缺陷
- **叶子节点**: 具体缺陷项
  - [1] VulnCore/JDBCAttack/src... - 序号和文件路径
  - [3] ulnCore/Inject/SQL/src... - 序号和文件路径
  - 等等...

### 搜索功能
- 顶部搜索框：支持按缺陷路径名称搜索
- 实时筛选显示匹配的缺陷项

## 核心接口

### 1. 获取任务缺陷树形结构
**GET** `/api/tasks/{taskId}/defects/tree`

根据任务ID获取缺陷的分类树形结构，按漏洞类型分组显示。

**路径参数**:
- `taskId`: 任务ID (必填)

**查询参数**:
- `keyword`: 搜索关键词，支持缺陷路径名称模糊匹配
- `defectLevel`: 危险程度筛选 (高危/中危/低危)

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "totalCount": 17,
    "vulnerabilityTypes": [
      {
        "typeName": "SQL注入",
        "typeCount": 5,
        "defects": [
          {
            "defectId": 1,
            "sequenceNumber": 1,
            "defectPath": "VulnCore/JDBCAttack/src/main/java/hmy/BMIAttack.java",
            "defectLine": 22,
            "defectLevel": "高危",
            "verificationResult": "验证成功"
          },
          {
            "defectId": 3,
            "sequenceNumber": 3,
            "defectPath": "ulnCore/Inject/SQL/src/main/java/com/spp/mysql/MysqlInject.java",
            "defectLine": 12,
            "defectLevel": "高危",
            "verificationResult": "验证失败"
          }
        ]
      },
      {
        "typeName": "RCE",
        "typeCount": 12,
        "defects": [
          {
            "defectId": 2,
            "sequenceNumber": 2,
            "defectPath": "VulnCore/XXE/src/main/java/org/example/XXEAttack.java",
            "defectLine": 56,
            "defectLevel": "高危",
            "verificationResult": "验证成功"
          }
        ]
      }
    ]
  }
}
```

### 2. 获取任务缺陷分类统计
**GET** `/api/tasks/{taskId}/defects/tree/statistics`

获取指定任务的缺陷按漏洞类型分类的统计信息。

**响应示例**:
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "totalCount": 17,
    "typeStatistics": [
      {
        "typeName": "SQL注入",
        "count": 5,
        "percentage": 29.4
      },
      {
        "typeName": "RCE", 
        "count": 12,
        "percentage": 70.6
      }
    ]
  }
}
```

## 数据模型

### 缺陷树形结构 (DefectTree)
```json
{
  "totalCount": 17,           // 缺陷总数
  "vulnerabilityTypes": [     // 漏洞类型分组列表
    {
      "typeName": "SQL注入",   // 漏洞类型名称
      "typeCount": 5,         // 该类型缺陷数量
      "defects": [            // 缺陷列表
        {
          "defectId": 1,                    // 缺陷ID
          "sequenceNumber": 1,              // 序号
          "defectPath": "VulnCore/...",     // 缺陷对象路径
          "defectLine": 22,                 // 缺陷行号
          "defectLevel": "高危",            // 危险程度
          "verificationResult": "验证成功"   // 验证结果
        }
      ]
    }
  ]
}
```

### 数据库字段映射

| 树形结构字段 | 数据库字段 | 说明 |
|-------------|-----------|------|
| totalCount | COUNT(*) | 任务下缺陷总数统计 |
| typeName | vulnerability_type | 漏洞类型 |
| typeCount | COUNT(*) GROUP BY | 按类型分组统计 |
| defectId | id | 缺陷主键ID |
| sequenceNumber | ROW_NUMBER() | 按创建时间排序的序号 |
| defectPath | defect_path | 缺陷文件路径 |
| defectLine | defect_line | 缺陷代码行号 |
| defectLevel | defect_level | 危险程度 |
| verificationResult | defect_verification_result | 验证结果 |

## 业务逻辑

### 1. 数据查询逻辑
```sql
-- 基础查询SQL示例
SELECT 
    d.id as defectId,
    d.vulnerability_type,
    d.defect_path,
    d.defect_line,
    d.defect_level,
    d.defect_verification_result,
    ROW_NUMBER() OVER (ORDER BY d.created_time) as sequenceNumber
FROM app_sast_defect_info d
WHERE d.task_id = ? 
  AND d.del_flag = 0
  AND (? IS NULL OR d.defect_path LIKE CONCAT('%', ?, '%'))
  AND (? IS NULL OR d.defect_level = ?)
ORDER BY d.vulnerability_type, d.created_time;
```

### 2. 树形结构构建逻辑
1. **查询数据**: 根据taskId查询所有相关缺陷
2. **分组处理**: 按vulnerability_type字段分组
3. **统计计算**: 计算总数和各类型数量
4. **排序处理**: 按创建时间排序生成序号
5. **树形组装**: 构建树形JSON结构

### 3. 搜索筛选逻辑
- **关键词搜索**: 对defect_path字段进行模糊匹配
- **等级筛选**: 精确匹配defect_level字段
- **实时筛选**: 前端可实现实时搜索效果

## 前端实现建议

### 1. 树形组件结构
```javascript
// 树形数据结构示例
const treeData = [
  {
    key: 'all',
    title: 'ALL(17)',
    children: [
      {
        key: 'sql-injection',
        title: 'SQL注入(5)',
        children: [
          {
            key: 'defect-1',
            title: '[1] VulnCore/JDBCAttack/src...',
            isLeaf: true,
            defectId: 1
          }
        ]
      },
      {
        key: 'rce',
        title: 'RCE(12)',
        children: []
      }
    ]
  }
];
```

### 2. 搜索功能实现
```javascript
// 搜索筛选函数
const filterTreeData = (data, keyword) => {
  if (!keyword) return data;
  
  return data.map(node => {
    const children = node.children?.filter(child => 
      child.defectPath?.toLowerCase().includes(keyword.toLowerCase())
    );
    return { ...node, children };
  }).filter(node => node.children?.length > 0);
};
```

### 3. 交互功能
- **节点展开/收起**: 支持分类节点的展开收起
- **点击跳转**: 点击具体缺陷项跳转到详情页
- **右键菜单**: 提供验证、修复等快捷操作
- **拖拽排序**: 支持缺陷项的拖拽排序（可选）

## 性能优化

### 1. 数据查询优化
- 在task_id和vulnerability_type字段上建立复合索引
- 使用分页加载避免一次性加载大量数据
- 缓存常用的统计数据

### 2. 前端渲染优化
- 使用虚拟滚动处理大量节点
- 懒加载子节点数据
- 防抖处理搜索输入

### 3. 接口优化
- 支持按需加载子节点数据
- 提供轻量级的统计接口
- 使用WebSocket推送实时更新

## 扩展功能

### 1. 高级筛选
- 按验证结果筛选
- 按危险等级筛选
- 按创建时间范围筛选
- 多条件组合筛选

### 2. 批量操作
- 批量选择缺陷项
- 批量验证操作
- 批量修复操作
- 批量导出功能

### 3. 可视化增强
- 不同危险等级使用不同颜色标识
- 验证状态图标显示
- 进度条显示修复进度
- 统计图表展示分布情况

## 错误处理

### 常见错误场景
- 任务ID不存在 (404)
- 任务无权限访问 (403)
- 搜索关键词格式错误 (400)
- 服务器查询超时 (500)

### 错误响应示例
```json
{
  "code": 404,
  "msg": "任务不存在",
  "data": null
}
```

## 总结

该接口完美支持原型图中展示的树形结构功能，提供了：

1. **完整的数据结构**: 支持多层级树形展示
2. **灵活的搜索筛选**: 支持关键词和条件筛选
3. **丰富的统计信息**: 提供分类统计和占比计算
4. **良好的扩展性**: 支持后续功能扩展
5. **优秀的性能**: 考虑了大数据量的处理优化

接口设计遵循RESTful规范，响应格式统一，便于前端开发和后续维护。
