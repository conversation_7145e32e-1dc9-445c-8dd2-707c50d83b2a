-- auto-generated definition
create table app_sast_defect_info
(
    id                         bigint      default snow_next_id_bigint() not null
        primary key,
    task_id                    bigint                                    not null
        constraint fk_defect_task
            references app_sast_task_management,
    project_id                 bigint                                    not null
        constraint fk_defect_project
            references app_sast_project_management,
    vulnerability_type         varchar(100)                              not null,
    defect_path                varchar(255)                              not null,
    defect_line                integer                                   not null,
    defect_level               varchar(50)                               not null,
    defect_verification_result varchar(50) default '未验证'::character varying,
    defect_fix_suggestion      text                                      not null,
    defect_chain_info          jsonb,
    defect_llm_results         jsonb,
    created_id                 bigint                                    not null,
    created_name               varchar(50)                               not null,
    created_time               timestamp   default now(),
    modify_id                  bigint,
    modify_name                varchar(50),
    modify_time                timestamp   default now(),
    del_flag                   smallint    default '0'::smallint         not null
);

comment on table app_sast_defect_info is 'SAST缺陷信息表';

comment on column app_sast_defect_info.id is '主键ID，自增';

comment on column app_sast_defect_info.task_id is '所属任务ID，外键关联';

comment on column app_sast_defect_info.project_id is '所属项目ID，外键关联';

comment on column app_sast_defect_info.vulnerability_type is '漏洞类型，如SQL注入/XSS/命令注入等';

comment on column app_sast_defect_info.defect_path is '缺陷路径，必填';

comment on column app_sast_defect_info.defect_line is '缺陷行号，必填';

comment on column app_sast_defect_info.defect_level is '危险程度：高危/中危/低危';

comment on column app_sast_defect_info.defect_verification_result is '验证结果：成功/失败/未验证';

comment on column app_sast_defect_info.defect_fix_suggestion is '修复建议，必填';

comment on column app_sast_defect_info.defect_chain_info is '整条链路信息，CodeQL链路数据';

comment on column app_sast_defect_info.defect_llm_results is '大模型检测结果，array[json]格式，多个大模型结果';

comment on column app_sast_defect_info.created_name is '创建人';

comment on column app_sast_defect_info.created_time is '创建时间';

comment on column app_sast_defect_info.modify_id is '更新人标识';

comment on column app_sast_defect_info.modify_name is '更新人';

comment on column app_sast_defect_info.modify_time is '更新时间';

comment on column app_sast_defect_info.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_defect_info
    owner to postgres;

create index idx_app_sast_defect_info_task_id
    on app_sast_defect_info (task_id);

create index idx_app_sast_defect_info_project_id
    on app_sast_defect_info (project_id);

create index idx_app_sast_defect_info_vulnerability_type
    on app_sast_defect_info (vulnerability_type);

create index idx_app_sast_defect_info_severity_level
    on app_sast_defect_info (defect_level);

