openapi: 3.0.3
info:
  title: 项目管理API
  description: SAST项目管理系统接口文档
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.example.com
    description: 生产环境

paths:
  /project-management/list:
    get:
      tags:
        - 项目管理
      summary: 获取项目管理列表
      description: 分页获取项目管理列表，包含项目基本信息、任务统计、产品阶段等信息
      operationId: getProjectList
      parameters:
        - name: page
          in: query
          description: 页码，从1开始
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: size
          in: query
          description: 每页大小
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
        - name: keyword
          in: query
          description: 搜索关键词（支持项目名称、任务名称、创建人模糊搜索）
          required: false
          schema:
            type: string
            maxLength: 255
            example: "测试项目"
      responses:
        '200':
          description: 成功获取项目列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 响应状态码
                    example: 200
                  msg:
                    type: string
                    description: 响应消息
                    example: "操作成功!"
                  result:
                    type: object
                    properties:
                      data:
                        type: array
                        description: 项目列表数据
                        items:
                          $ref: '#/components/schemas/ProjectListItem'
                      total:
                        type: integer
                        description: 总记录数
                        example: 100
                      num:
                        type: integer
                        description: 当前页记录数
                        example: 10
              examples:
                success:
                  summary: 成功响应示例
                  value:
                    code: 200
                    msg: "操作成功!"
                    result:
                      data:
                        - id: 1
                          projectName: "测试智能化系统安全分析项目"
                          projectDescription: "通过智能化技术对系统进行全方位的安全分析和漏洞检测"
                          taskStats: "5/8"
                          vulnerabilityStats:
                            highRisk: 100
                            mediumRisk: 467
                            lowRisk: 379
                          productStages:
                            - name: "POC"
                              status: "已完成"
                            - name: "测试"
                              status: "进行中"
                            - name: "生产"
                              status: "未开始"
                          createdTime: "2025-03-12T12:23:11"
                      total: 100
                      num: 10
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 400
                msg: "参数错误"
                data: null
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 500
                msg: "操作失败"
                data: null

components:
  schemas:
    ProjectListItem:
      type: object
      description: 项目列表项
      required:
        - id
        - projectName
        - createdTime
      properties:
        id:
          type: integer
          format: int64
          description: 项目ID
          example: 1
        projectName:
          type: string
          description: 项目名称
          maxLength: 255
          example: "测试智能化系统安全分析项目"
        projectDescription:
          type: string
          description: 项目描述
          example: "通过智能化技术对系统进行全方位的安全分析和漏洞检测"
        taskStats:
          type: string
          description: 任务统计（格式：已完成/总数）
          example: "5/8"
        vulnerabilityStats:
          $ref: '#/components/schemas/VulnerabilityStats'
        productStages:
          type: array
          description: 产品阶段列表
          items:
            $ref: '#/components/schemas/ProductStage'
        createdTime:
          type: string
          format: date-time
          description: 创建时间
          example: "2025-03-12T12:23:11"

    VulnerabilityStats:
      type: object
      description: 漏洞统计
      properties:
        highRisk:
          type: integer
          description: 高危漏洞数量
          minimum: 0
          example: 100
        mediumRisk:
          type: integer
          description: 中危漏洞数量
          minimum: 0
          example: 467
        lowRisk:
          type: integer
          description: 低危漏洞数量
          minimum: 0
          example: 379

    ProductStage:
      type: object
      description: 产品阶段
      required:
        - name
        - status
      properties:
        name:
          type: string
          description: 阶段名称
          enum: ["POC", "测试", "生产"]
          example: "POC"
        status:
          type: string
          description: 阶段状态
          enum: ["未开始", "进行中", "已完成", "失败"]
          example: "已完成"

    ErrorResponse:
      type: object
      description: 错误响应
      required:
        - code
        - msg
      properties:
        code:
          type: integer
          description: 错误码
          example: 500
        msg:
          type: string
          description: 错误消息
          example: "操作失败"
        data:
          type: object
          nullable: true
          description: 错误详情
          example: null

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: 项目管理
    description: 项目管理相关接口
