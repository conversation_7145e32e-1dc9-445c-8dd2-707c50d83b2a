# 规则一：带分页的列表查询，示例响应如下。

{
    "code": 0,
    "msg": "string",
    "data": {
        "records": [
            {
                "id": "string",
                "vulName": "string",
                "affectTarget": "string",
                "vulStatus": "string",
                "vulLevel": "string",
                "netLocation": "string",
                "belongAsset": "string",
                "belongBizSystem": "string",
                "securityPerson": "string",
                "operPerson": "string",
                "firstFindTime": "string",
                "lastFindTime": "string"
            }
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "pages": 0
    }
}

# 规则二：普通的响应格式，示例响应如下。

{
    "code": 0,
    "msg": "string",
    "data": {
        "id": "string",
        "assetCode": "string",
        "assetName": "string",
        "assetType": "string",
        "assetStatus": "string",
        "manufacturer": "string",
        "assetModel": "string",
        "isLocalization": "string",
        "provice": "string",
        "city": "string",
        "dataCenter": "string",
        "build": "string",
        "machineRoom": "string",
        "rowNumber": "string",
        "machineRack": "string",
        "confidentiality": 0,
        "completeness": 0,
        "availability": 0,
        "assetLevel": 0,
        "contactId": "string",
        "contactPerson": "string",
        "contactPhone": "string",
        "contactLandline": "string",
        "contactEmail": "string",
        "securityPerson": "string",
        "securityPhone": "string",
        "securityLandline": "string",
        "securityEmail": "string",
        "operPerson": "string",
        "operPhone": "string",
        "operLandline": "string",
        "operEmail": "string",
        "orgName": "string",
        "internetExposureDto": [
            {
                "id": "string",
                "exposureAddr": "string",
                "exposureExplain": "string"
            }
        ],
        "bizSystemDto": [
            {
                "id": "string",
                "bizCode": "string",
                "bizName": "string",
                "contactPerson": [
                    "string"
                ],
                "createdTime": "string"
            }
        ],
        "networkDto": [
            {
                "id": "string",
                "netLocaltion": "string",
                "ipType": "string",
                "ipAddr": "string"
            }
        ]
    }
}
