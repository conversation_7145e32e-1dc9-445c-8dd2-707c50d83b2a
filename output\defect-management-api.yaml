openapi: 3.0.3
info:
  title: 缺陷管理API
  description: SAST缺陷管理相关接口文档
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.example.com
    description: 生产环境

paths:
  /defects/statistics:
    get:
      tags:
        - 缺陷管理
      summary: 获取缺陷统计信息
      description: 获取缺陷总数和各危险等级的统计数据
      operationId: getDefectStatistics
      parameters:
        - name: projectId
          in: query
          description: 项目ID（可选，不传则统计所有项目）
          required: false
          schema:
            type: integer
            format: int64
            example: 1
        - name: taskId
          in: query
          description: 任务ID（可选，不传则统计所有任务）
          required: false
          schema:
            type: integer
            format: int64
            example: 123456
      responses:
        '200':
          description: 成功获取缺陷统计信息
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    $ref: '#/components/schemas/DefectStatistics'
              example:
                code: 0
                msg: "string"
                data:
                  totalCount: 265
                  highRiskCount: 140
                  highRiskPercentage: 30
                  mediumRiskCount: 109
                  mediumRiskPercentage: 30
                  lowRiskCount: 87
                  lowRiskPercentage: 40
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /defects:
    get:
      tags:
        - 缺陷管理
      summary: 获取缺陷列表（分页）
      description: 分页查询缺陷信息列表，支持搜索和筛选
      operationId: getDefectList
      parameters:
        - name: current
          in: query
          description: 当前页码
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: size
          in: query
          description: 每页记录数
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
        - name: keyword
          in: query
          description: 搜索关键词（缺陷名称/漏洞类型）
          required: false
          schema:
            type: string
            example: "SQL注入"
        - name: projectId
          in: query
          description: 项目ID筛选
          required: false
          schema:
            type: integer
            format: int64
            example: 1
        - name: taskId
          in: query
          description: 任务ID筛选
          required: false
          schema:
            type: integer
            format: int64
            example: 123456
        - name: defectLevel
          in: query
          description: 危险程度筛选
          required: false
          schema:
            type: string
            enum: ["高危", "中危", "低危"]
            example: "高危"
        - name: verificationResult
          in: query
          description: 验证结果筛选
          required: false
          schema:
            type: string
            enum: ["验证成功", "验证失败", "未验证"]
            example: "验证成功"
      responses:
        '200':
          description: 成功获取缺陷列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      records:
                        type: array
                        items:
                          $ref: '#/components/schemas/DefectInfo'
                      total:
                        type: integer
                        description: 总记录数
                        example: 265
                      size:
                        type: integer
                        description: 每页记录数
                        example: 10
                      current:
                        type: integer
                        description: 当前页码
                        example: 1
                      pages:
                        type: integer
                        description: 总页数
                        example: 27
              example:
                code: 0
                msg: "string"
                data:
                  records:
                    - id: 1
                      sequenceNumber: "01"
                      vulnerabilityType: "SQL注入"
                      projectName: "测试智能检测试"
                      defectPath: "VulnCore/JDBCAttack/src/main/java/hmy/BMIAttack.java"
                      defectLine: 22
                      defectLevel: "高危"
                      verificationResult: "验证成功"
                      createdTime: "2025-03-12 12:23:11"
                      taskId: 123456
                      projectId: 1
                  total: 265
                  size: 10
                  current: 1
                  pages: 27
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /defects/{defectId}:
    get:
      tags:
        - 缺陷管理
      summary: 获取缺陷详情
      description: 获取指定缺陷的详细信息
      operationId: getDefectDetail
      parameters:
        - name: defectId
          in: path
          description: 缺陷ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      responses:
        '200':
          description: 成功获取缺陷详情
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    $ref: '#/components/schemas/DefectDetail'
        '404':
          description: 缺陷不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /defects/{defectId}/fix:
    post:
      tags:
        - 缺陷管理
      summary: 修复缺陷
      description: 标记缺陷为已修复状态
      operationId: fixDefect
      parameters:
        - name: defectId
          in: path
          description: 缺陷ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                fixNote:
                  type: string
                  description: 修复说明
                  example: "已按照建议修复SQL注入漏洞"
      responses:
        '200':
          description: 缺陷修复成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    nullable: true
                    example: null
        '404':
          description: 缺陷不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /tasks/{taskId}/defects/tree:
    get:
      tags:
        - 缺陷管理
      summary: 获取任务缺陷树形结构
      description: 根据任务ID获取缺陷的分类树形结构，按漏洞类型分组显示
      operationId: getTaskDefectTree
      parameters:
        - name: taskId
          in: path
          description: 任务ID
          required: true
          schema:
            type: integer
            format: int64
            example: 123456
        - name: keyword
          in: query
          description: 搜索关键词（缺陷路径名称）
          required: false
          schema:
            type: string
            example: "VulnCore"
      responses:
        '200':
          description: 成功获取缺陷树形结构
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    $ref: '#/components/schemas/DefectTree'
              example:
                code: 0
                msg: "string"
                data:
                  totalCount: 17
                  vulnerabilityTypes:
                    - typeName: "SQL注入"
                      typeCount: 5
                      defects:
                        - defectId: 1
                          sequenceNumber: 1
                          defectPath: "VulnCore/JDBCAttack/src/main/java/hmy/BMIAttack.java"
                          defectLine: 22
                          defectLevel: "高危"
                        - defectId: 3
                          sequenceNumber: 3
                          defectPath: "ulnCore/Inject/SQL/src/main/java/com/spp/mysql/MysqlInject.java"
                          defectLine: 12
                          defectLevel: "高危"
                        - defectId: 6
                          sequenceNumber: 6
                          defectPath: "VulnCore/JDBCAttack/src/main/java/hzd/database/HZAttack.java"
                          defectLine: 13
                          defectLevel: "中危"
                        - defectId: 8
                          sequenceNumber: 8
                          defectPath: "VulnCore/Inject/SQL/src/main/java/com/spp/mysql/SpringJDBCInject.java"
                          defectLine: 11
                          defectLevel: "低危"
                        - defectId: 10
                          sequenceNumber: 10
                          defectPath: "VulnCore/Inject/SQL/src/main/java/com/spp/mysql/MysqlInject.java"
                          defectLine: 93
                          defectLevel: "低危"
                    - typeName: "RCE"
                      typeCount: 12
                      defects: []
        '404':
          description: 任务不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    DefectStatistics:
      type: object
      description: 缺陷统计信息
      properties:
        totalCount:
          type: integer
          description: 漏洞总数
          example: 265
        highRiskCount:
          type: integer
          description: 高危漏洞数量
          example: 140
        highRiskPercentage:
          type: integer
          description: 高危漏洞占比（百分比）
          example: 30
        mediumRiskCount:
          type: integer
          description: 中危漏洞数量
          example: 109
        mediumRiskPercentage:
          type: integer
          description: 中危漏洞占比（百分比）
          example: 30
        lowRiskCount:
          type: integer
          description: 低危漏洞数量
          example: 87
        lowRiskPercentage:
          type: integer
          description: 低危漏洞占比（百分比）
          example: 40

    DefectInfo:
      type: object
      description: 缺陷信息
      properties:
        id:
          type: integer
          format: int64
          description: 缺陷ID
          example: 1
        sequenceNumber:
          type: string
          description: 序号
          example: "01"
        vulnerabilityType:
          type: string
          description: 漏洞类型
          example: "SQL注入"
        projectName:
          type: string
          description: 所属项目名称
          example: "测试智能检测试"
        defectPath:
          type: string
          description: 缺陷对象路径
          example: "VulnCore/JDBCAttack/src/main/java/hmy/BMIAttack.java"
        defectLine:
          type: integer
          description: 缺陷行号
          example: 22
        defectLevel:
          type: string
          description: 危险程度
          enum: ["高危", "中危", "低危"]
          example: "高危"
        verificationResult:
          type: string
          description: 验证结果
          enum: ["验证成功", "验证失败", "未验证"]
          example: "验证成功"
        createdTime:
          type: string
          description: 创建时间
          example: "2025-03-12 12:23:11"
        taskId:
          type: integer
          format: int64
          description: 所属任务ID
          example: 123456
        projectId:
          type: integer
          format: int64
          description: 所属项目ID
          example: 1

    DefectDetail:
      type: object
      description: 缺陷详细信息
      allOf:
        - $ref: '#/components/schemas/DefectInfo'
        - type: object
          properties:
            defectFixSuggestion:
              type: string
              description: 修复建议
              example: "使用参数化查询或预编译语句来防止SQL注入攻击"
            defectChainInfo:
              type: object
              description: 缺陷链路信息
              example: {}
            defectLlmResults:
              type: array
              description: 大模型检测结果
              items:
                type: object
              example: []

    DefectTree:
      type: object
      description: 缺陷树形结构
      properties:
        totalCount:
          type: integer
          description: 缺陷总数
          example: 17
        vulnerabilityTypes:
          type: array
          description: 漏洞类型分组列表
          items:
            $ref: '#/components/schemas/VulnerabilityTypeGroup'

    VulnerabilityTypeGroup:
      type: object
      description: 漏洞类型分组
      properties:
        typeName:
          type: string
          description: 漏洞类型名称
          example: "SQL注入"
        typeCount:
          type: integer
          description: 该类型缺陷数量
          example: 5
        defects:
          type: array
          description: 缺陷列表
          items:
            $ref: '#/components/schemas/DefectTreeItem'

    DefectTreeItem:
      type: object
      description: 缺陷树节点项
      properties:
        defectId:
          type: integer
          format: int64
          description: 缺陷ID
          example: 1
        sequenceNumber:
          type: integer
          description: 序号
          example: 1
        defectPath:
          type: string
          description: 缺陷对象路径
          example: "VulnCore/JDBCAttack/src/main/java/hmy/BMIAttack.java"
        defectLine:
          type: integer
          description: 缺陷行号
          example: 22
        defectLevel:
          type: string
          description: 危险程度
          enum: ["高危", "中危", "低危"]
          example: "高危"

    ErrorResponse:
      type: object
      description: 错误响应
      required:
        - code
        - msg
      properties:
        code:
          type: integer
          description: 错误码
          example: 500
        msg:
          type: string
          description: 错误消息
          example: "操作失败"
        data:
          type: object
          nullable: true
          description: 错误详情
          example: null

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: 缺陷管理
    description: 缺陷信息管理相关接口
