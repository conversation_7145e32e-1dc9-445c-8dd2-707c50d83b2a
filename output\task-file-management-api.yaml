openapi: 3.0.3
info:
  title: 任务文件管理API
  description: SAST任务文件管理相关接口文档
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.example.com
    description: 生产环境

paths:
  /tasks/{taskId}/files:
    get:
      tags:
        - 文件管理
      summary: 获取任务文件列表
      description: 获取指定任务的所有上传文件列表
      operationId: getTaskFiles
      parameters:
        - name: taskId
          in: path
          description: 任务ID
          required: true
          schema:
            type: integer
            format: int64
            example: 123456
      responses:
        '200':
          description: 成功获取文件列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/TaskFile'
              example:
                code: 0
                msg: "string"
                data:
                  - fileId: 789
                    fileName: "xxx模型源码编译文件.xlsx"
                    fileType: "源码文件"
                    fileSize: 1048576
                    uploadTime: "2025-01-29T10:30:00"
                    uploadStatus: "上传中"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /tasks/{taskId}/files/{fileId}:
    delete:
      tags:
        - 文件管理
      summary: 删除任务文件
      description: 删除指定任务的指定文件
      operationId: deleteTaskFile
      parameters:
        - name: taskId
          in: path
          description: 任务ID
          required: true
          schema:
            type: integer
            format: int64
            example: 123456
        - name: fileId
          in: path
          description: 文件ID
          required: true
          schema:
            type: integer
            format: int64
            example: 789
      responses:
        '200':
          description: 文件删除成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    nullable: true
                    example: null
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /tasks/{taskId}/start:
    post:
      tags:
        - 任务管理
      summary: 启动任务分析
      description: 启动指定任务的安全分析流程
      operationId: startTaskAnalysis
      parameters:
        - name: taskId
          in: path
          description: 任务ID
          required: true
          schema:
            type: integer
            format: int64
            example: 123456
      responses:
        '200':
          description: 任务启动成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    type: object
                    properties:
                      taskId:
                        type: integer
                        format: int64
                        description: 任务ID
                        example: 123456
                      taskStatus:
                        type: string
                        description: 任务状态
                        example: "传播器查找"
                      startTime:
                        type: string
                        format: date-time
                        description: 启动时间
                        example: "2025-01-29T10:35:00"
              example:
                code: 0
                msg: "string"
                data:
                  taskId: 123456
                  taskStatus: "传播器查找"
                  startTime: "2025-01-29T10:35:00"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /tasks/{taskId}/status:
    get:
      tags:
        - 任务管理
      summary: 获取任务状态
      description: 获取指定任务的当前状态和进度信息
      operationId: getTaskStatus
      parameters:
        - name: taskId
          in: path
          description: 任务ID
          required: true
          schema:
            type: integer
            format: int64
            example: 123456
      responses:
        '200':
          description: 成功获取任务状态
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: "string"
                  data:
                    $ref: '#/components/schemas/TaskStatus'
              example:
                code: 0
                msg: "string"
                data:
                  taskId: 123456
                  taskName: "struts检测"
                  taskStatus: "链路分析"
                  progress: 45
                  currentStage: "链路分析"
                  stages:
                    - stageName: "传播器查找"
                      stageStatus: "已完成"
                      startTime: "2025-01-29T10:35:00"
                      endTime: "2025-01-29T10:40:00"
                    - stageName: "链路分析"
                      stageStatus: "进行中"
                      startTime: "2025-01-29T10:40:00"
                      endTime: null
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    TaskFile:
      type: object
      description: 任务文件信息
      properties:
        fileId:
          type: integer
          format: int64
          description: 文件ID
          example: 789
        fileName:
          type: string
          description: 文件名
          example: "xxx模型源码编译文件.xlsx"
        fileType:
          type: string
          description: 文件类型
          enum: ["源码文件", "预编译压缩包"]
          example: "源码文件"
        fileSize:
          type: integer
          format: int64
          description: 文件大小（字节）
          example: 1048576
        uploadTime:
          type: string
          format: date-time
          description: 上传时间
          example: "2025-01-29T10:30:00"
        uploadStatus:
          type: string
          description: 上传状态
          enum: ["上传中", "上传完成", "上传失败"]
          example: "上传完成"

    TaskStatus:
      type: object
      description: 任务状态信息
      properties:
        taskId:
          type: integer
          format: int64
          description: 任务ID
          example: 123456
        taskName:
          type: string
          description: 任务名称
          example: "struts检测"
        taskStatus:
          type: string
          description: 任务状态
          enum: ["排队", "传播器查找", "链路分析", "研判", "POC验证", "修复建议生成", "AI分析完成", "分析失败"]
          example: "链路分析"
        progress:
          type: integer
          description: 任务进度百分比
          minimum: 0
          maximum: 100
          example: 45
        currentStage:
          type: string
          description: 当前执行阶段
          example: "链路分析"
        stages:
          type: array
          description: 任务执行阶段列表
          items:
            $ref: '#/components/schemas/TaskStage'

    TaskStage:
      type: object
      description: 任务执行阶段
      properties:
        stageName:
          type: string
          description: 阶段名称
          example: "传播器查找"
        stageStatus:
          type: string
          description: 阶段状态
          enum: ["未开始", "进行中", "已完成", "失败"]
          example: "已完成"
        startTime:
          type: string
          format: date-time
          description: 开始时间
          example: "2025-01-29T10:35:00"
        endTime:
          type: string
          format: date-time
          nullable: true
          description: 结束时间
          example: "2025-01-29T10:40:00"

    ErrorResponse:
      type: object
      description: 错误响应
      required:
        - code
        - msg
      properties:
        code:
          type: integer
          description: 错误码
          example: 500
        msg:
          type: string
          description: 错误消息
          example: "操作失败"
        data:
          type: object
          nullable: true
          description: 错误详情
          example: null

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: 任务管理
    description: 任务状态管理相关接口
  - name: 文件管理
    description: 任务文件管理相关接口
