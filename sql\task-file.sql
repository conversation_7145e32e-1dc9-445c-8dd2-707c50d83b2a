-- auto-generated definition
create table app_sast_task_files
(
    id           bigint       default snow_next_id_bigint() not null
        primary key,
    task_id      bigint                                     not null
        constraint fk_task_files_task
            references app_sast_task_management,
    file_type    varchar(50)                                not null,
    file_name    varchar(255)                               not null,
    file_path    varchar(500)                               not null,
    file_size    bigint,
    file_hash    varchar(64),
    upload_time  timestamp(6) default CURRENT_TIMESTAMP(6),
    created_id   bigint                                     not null,
    created_name varchar(50)                                not null,
    created_time timestamp    default now(),
    modify_id    bigint,
    modify_name  varchar(50),
    modify_time  timestamp    default now(),
    del_flag     smallint     default 0                     not null
);

comment on table app_sast_task_files is 'SAST任务文件存储表';

comment on column app_sast_task_files.id is '主键ID';

comment on column app_sast_task_files.task_id is '关联任务ID，外键关联';

comment on column app_sast_task_files.file_type is '文件类型：预编译压缩包/源码文件';

comment on column app_sast_task_files.file_name is '文件名，必填';

comment on column app_sast_task_files.file_path is '文件存储路径，必填';

comment on column app_sast_task_files.file_size is '文件大小(字节)';

comment on column app_sast_task_files.file_hash is '文件哈希值(MD5/SHA256)';

comment on column app_sast_task_files.upload_time is '上传时间，精确到微秒';

comment on column app_sast_task_files.created_id is '创建人ID';

comment on column app_sast_task_files.created_name is '创建人姓名';

comment on column app_sast_task_files.created_time is '创建时间';

comment on column app_sast_task_files.modify_id is '更新人ID';

comment on column app_sast_task_files.modify_name is '更新人姓名';

comment on column app_sast_task_files.modify_time is '更新时间';

comment on column app_sast_task_files.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_task_files
    owner to postgres;

create index idx_app_sast_task_files_task_id
    on app_sast_task_files (task_id);

create index idx_app_sast_task_files_file_type
    on app_sast_task_files (file_type);

