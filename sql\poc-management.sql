-- auto-generated definition
create table app_sast_poc_management
(
    id                   bigint       default snow_next_id_bigint() not null
        primary key,
    defect_id            bigint                                     not null
        constraint fk_poc_defect
            references app_sast_defect_info,
    poc_content          text                                       not null,
    verification_message text,
    generation_time      timestamp(6) default CURRENT_TIMESTAMP(6),
    verification_result  varchar(50)  default '未验证'::character varying,
    generation_model     varchar(255),
    created_id           bigint                                     not null,
    created_name         varchar(50)                                not null,
    created_time         timestamp    default now(),
    modify_id            bigint,
    modify_name          varchar(50),
    modify_time          timestamp    default now(),
    del_flag             smallint     default '0'::smallint         not null
);

comment on table app_sast_poc_management is 'SAST POC管理表';

comment on column app_sast_poc_management.id is '主键ID，自增';

comment on column app_sast_poc_management.defect_id is '关联缺陷ID，外键关联';

comment on column app_sast_poc_management.poc_content is 'POC内容，Nuclei YAML格式';

comment on column app_sast_poc_management.verification_message is '验证数据包，TXT格式';

comment on column app_sast_poc_management.generation_time is 'POC生成时间，精确到微秒';

comment on column app_sast_poc_management.verification_result is '验证结果：成功/失败/未验证';

comment on column app_sast_poc_management.generation_model is '生成POC的模型名称';

comment on column app_sast_poc_management.created_name is '创建人';

comment on column app_sast_poc_management.created_time is '创建时间';

comment on column app_sast_poc_management.modify_id is '更新人标识';

comment on column app_sast_poc_management.modify_name is '更新人';

comment on column app_sast_poc_management.modify_time is '更新时间';

comment on column app_sast_poc_management.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_poc_management
    owner to postgres;

create index idx_app_sast_poc_management_defect_id
    on app_sast_poc_management (defect_id);

create index idx_app_sast_poc_management_verification_status
    on app_sast_poc_management (verification_result);

